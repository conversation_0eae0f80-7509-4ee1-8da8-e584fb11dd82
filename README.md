# Video Cleaner - Foul Word Muter

A professional tool that automatically detects and mutes foul words in videos using AI speech recognition with optional subtitle verification.

## Features

- **Modern GUI Interface**: Beautiful, intuitive graphical interface with real-time progress tracking
- **Command Line Interface**: Traditional command-line interface for automation and scripting
- **AI-Powered Detection**: Uses OpenAI's Whisper model for accurate speech recognition
- **Dual Processing Modes**:
  - **AI-Only Mode**: Fast processing using only Whisper AI detection
  - **Subtitle Verification Mode**: Enhanced accuracy by cross-referencing with subtitle files
- **Flexible Input**: Subtitle files are now optional - works with or without them
- **Real-time Logging**: Live progress updates with color-coded logging levels
- **Automatic Reports**: Generates detailed processing reports
- **Professional Design**: Clean, modern interface with emoji icons and intuitive layout

## Quick Start

### GUI Mode (Recommended)

1. **Double-click** `launch_video_cleaner.bat` to start the GUI

   OR

   Run: `python code/video_cleaner.py --gui`

2. **Select your files**:
   - Video file (MP4, AVI, MOV, etc.) - **Required**
   - Subtitle file (.srt) - **Optional** (improves accuracy when provided)
   - Foul words file (default: foul_words.txt) - **Required**

3. **Choose processing mode**:
   - **With subtitles**: More accurate but requires subtitle file
   - **Without subtitles**: Faster AI-only processing

4. **Configure settings**:
   - Choose Whisper model size (medium recommended)
   - Set log level (INFO recommended)

5. **Click "Start Processing"** and monitor progress in real-time

### Command Line Mode

**With subtitle verification:**
```bash
python code/video_cleaner.py "video.mp4" "subtitles.srt" -w "foul_words.txt"
```

**AI-only mode (no subtitles):**
```bash
python code/video_cleaner.py "video.mp4" -w "foul_words.txt"
```

## GUI Features

### Main Tab
- **📁 File Selection**: Modern file browser with drag-and-drop style inputs
- **⚙️ Configuration**: Intuitive AI model selection and logging controls
- **📈 Progress Tracking**: Real-time status updates with animated progress bar
- **🚀 Control Buttons**: Large, colorful action buttons with emoji icons
- **💡 Smart Tooltips**: Helpful descriptions for each input field

### Logs Tab
- **📋 Real-time Logging**: Dark-themed console with color-coded log levels
- **🎨 Syntax Highlighting**: Different colors for INFO, WARNING, ERROR, etc.
- **📄 Detailed Information**: View detection results and verification details
- **🗑️ Log Management**: Easy log clearing and level adjustment

### Visual Improvements
- **🎬 Professional Header**: Branded header with application title
- **📱 Responsive Layout**: Scrollable interface that adapts to content
- **🎨 Modern Color Scheme**: Professional blue and gray color palette
- **📐 Clean Typography**: Consistent fonts and spacing throughout

## Requirements

- Python 3.7+
- Required packages:
  - whisper
  - stable-whisper
  - moviepy
  - srt
  - tkinter (usually included with Python)

## How It Works

### AI-Only Mode (No Subtitles)
1. **Audio Extraction**: Extracts audio from the video file
2. **Speech Recognition**: Uses Whisper AI to transcribe audio with word-level timestamps
3. **Foul Word Detection**: Identifies foul words in the transcription
4. **Video Processing**: Creates a new video with detected foul words muted
5. **Report Generation**: Creates detailed logs and reports

### Subtitle Verification Mode (With Subtitles)
1. **Audio Extraction**: Extracts audio from the video file
2. **Speech Recognition**: Uses Whisper AI to transcribe audio with word-level timestamps
3. **Foul Word Detection**: Identifies potential foul words in the transcription
4. **Subtitle Verification**: Cross-references detections with subtitle file for accuracy
5. **Video Processing**: Creates a new video with verified foul words muted
6. **Report Generation**: Creates detailed logs and reports

## File Outputs

- **Muted Video**:
  - With subtitles: `[original_name]_muted_verified.[ext]`
  - AI-only: `[original_name]_muted_ai.[ext]`
- **Processing Log**: `[original_name]_processing.log`
- **Summary Report**: `[original_name]_report.log`

## Tips

- Use the **medium** Whisper model for best balance of speed and accuracy
- **With subtitles**: Ensure your subtitle file is properly synced with the video for best results
- **Without subtitles**: AI-only mode is faster but may have more false positives
- **Subtitle verification mode** only mutes words verified in both audio and subtitles
- **AI-only mode** mutes all detected foul words based on Whisper transcription
- Processing time depends on video length and chosen AI model

## Troubleshooting

- If the GUI doesn't start, ensure Python and tkinter are properly installed
- For large videos, consider using a smaller Whisper model (base or small)
- Check the logs tab for detailed error information
- Ensure all input files exist and are accessible

## Command Line Options

```
usage: video_cleaner.py [-h] [--gui] [-w WORDS_FILE] [-m MODEL] [-o OUTPUT_PATH]
                        [--report_file REPORT_FILE] [--log_file LOG_FILE]
                        [--log_level {DEBUG,INFO,WARNING,ERROR,CRITICAL}]
                        [video_path] [srt_path]

options:
  --gui                 Launch the GUI version
  video_path            Path to input video file (required)
  srt_path              Path to subtitle file (optional - improves accuracy)
  -w, --words_file      Path to foul words file (default: foul_words.txt)
  -m, --model          Whisper model size (tiny, base, small, medium, large)
  -o, --output_path    Output video file path
  --log_level          Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
```
