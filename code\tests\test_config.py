"""
Unit tests for configuration management.
"""

import unittest
import tempfile
import os
import json
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from video_cleaner.core.config import ProcessingConfig, get_config, set_config


class TestProcessingConfig(unittest.TestCase):
    """Test cases for ProcessingConfig class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Save original config
        self.original_config = get_config()
    
    def tearDown(self):
        """Clean up after tests."""
        # Restore original config
        set_config(self.original_config)
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ProcessingConfig()
        
        self.assertEqual(config.model_size, "medium")
        self.assertEqual(config.mute_padding_start, 0.05)
        self.assertEqual(config.mute_padding_end, 0.05)
        self.assertEqual(config.temp_audio_filename, "temp_audio_for_processing.wav")
        self.assertEqual(config.output_suffix, "_muted_verified")
        self.assertEqual(config.video_codec, "libx264")
        self.assertEqual(config.audio_sample_rate, 44100)
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = ProcessingConfig(
            model_size="large",
            mute_padding_start=0.1,
            video_crf="20",
            max_retry_attempts=5
        )
        
        self.assertEqual(config.model_size, "large")
        self.assertEqual(config.mute_padding_start, 0.1)
        self.assertEqual(config.video_crf, "20")
        self.assertEqual(config.max_retry_attempts, 5)
        # Default values should still be present
        self.assertEqual(config.mute_padding_end, 0.05)
    
    def test_save_to_file(self):
        """Test saving configuration to file."""
        config = ProcessingConfig(model_size="large", mute_padding_start=0.2)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            # Test successful save
            success = config.save_to_file(config_file)
            self.assertTrue(success)
            self.assertTrue(os.path.exists(config_file))
            
            # Verify file contents
            with open(config_file, 'r') as f:
                data = json.load(f)
            
            self.assertEqual(data['model_size'], 'large')
            self.assertEqual(data['mute_padding_start'], 0.2)
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
    
    def test_load_from_file(self):
        """Test loading configuration from file."""
        # Create test config file
        test_data = {
            'model_size': 'small',
            'mute_padding_start': 0.3,
            'video_crf': '25'
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            config_file = f.name
        
        try:
            # Test successful load
            config = ProcessingConfig.load_from_file(config_file)
            
            self.assertEqual(config.model_size, 'small')
            self.assertEqual(config.mute_padding_start, 0.3)
            self.assertEqual(config.video_crf, '25')
            # Default values should still be present for unspecified fields
            self.assertEqual(config.mute_padding_end, 0.05)
            
        finally:
            os.unlink(config_file)
    
    def test_load_from_nonexistent_file(self):
        """Test loading from non-existent file returns default config."""
        config = ProcessingConfig.load_from_file("nonexistent_file.json")
        
        # Should return default config
        default_config = ProcessingConfig()
        self.assertEqual(config.model_size, default_config.model_size)
        self.assertEqual(config.mute_padding_start, default_config.mute_padding_start)
    
    def test_load_from_invalid_json(self):
        """Test loading from invalid JSON file returns default config."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            config_file = f.name
        
        try:
            config = ProcessingConfig.load_from_file(config_file)
            
            # Should return default config
            default_config = ProcessingConfig()
            self.assertEqual(config.model_size, default_config.model_size)
            
        finally:
            os.unlink(config_file)
    
    def test_global_config_management(self):
        """Test global configuration get/set functions."""
        # Test getting default config
        config = get_config()
        self.assertIsInstance(config, ProcessingConfig)
        
        # Test setting custom config
        custom_config = ProcessingConfig(model_size="tiny")
        set_config(custom_config)
        
        retrieved_config = get_config()
        self.assertEqual(retrieved_config.model_size, "tiny")
        self.assertIs(retrieved_config, custom_config)


class TestConfigIntegration(unittest.TestCase):
    """Integration tests for configuration system."""
    
    def test_config_persistence_workflow(self):
        """Test complete save/load workflow."""
        # Create custom config
        original_config = ProcessingConfig(
            model_size="large",
            mute_padding_start=0.15,
            mute_padding_end=0.25,
            video_crf="15",
            max_retry_attempts=5
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            # Save config
            success = original_config.save_to_file(config_file)
            self.assertTrue(success)
            
            # Load config
            loaded_config = ProcessingConfig.load_from_file(config_file)
            
            # Verify all custom values are preserved
            self.assertEqual(loaded_config.model_size, original_config.model_size)
            self.assertEqual(loaded_config.mute_padding_start, original_config.mute_padding_start)
            self.assertEqual(loaded_config.mute_padding_end, original_config.mute_padding_end)
            self.assertEqual(loaded_config.video_crf, original_config.video_crf)
            self.assertEqual(loaded_config.max_retry_attempts, original_config.max_retry_attempts)
            
            # Verify default values are still present
            self.assertEqual(loaded_config.audio_sample_rate, original_config.audio_sample_rate)
            self.assertEqual(loaded_config.video_codec, original_config.video_codec)
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)


if __name__ == '__main__':
    unittest.main()
