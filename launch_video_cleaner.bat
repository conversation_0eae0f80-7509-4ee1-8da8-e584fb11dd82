@echo off
echo Starting Video Cleaner GUI...

REM Ensure Voxtral (Mistral) server is running on http://127.0.0.1:8000/v1
set "API_BASE=http://127.0.0.1:8000/v1"
set "VOXTRAL_MODEL=mistralai/Voxtral-Mini-3B-2507"

REM Quick health check: /models
powershell -Command "try { $r=Invoke-WebRequest -UseBasicParsing -TimeoutSec 2 %API_BASE%/models; if($r.StatusCode -ge 200 -and $r.StatusCode -lt 300){ exit 0 } else { exit 1 } } catch { exit 1 }"
if not %errorlevel%==0 (
  echo Voxtral server not detected. Attempting to start it with vLLM...
  REM Optional: warn if vLLM not installed
  python -m pip show vllm >nul 2>&1 || echo Note: 'vllm' is not installed. Run: pip install vllm
  REM Start server in background and log output
  start "Voxtral Server" cmd /c "python -m vllm.entrypoints.openai.api_server --model %VOXTRAL_MODEL% --host 127.0.0.1 --port 8000 >> voxtral_server.log 2>&1"
)

python code\video_cleaner.py --gui
pause
