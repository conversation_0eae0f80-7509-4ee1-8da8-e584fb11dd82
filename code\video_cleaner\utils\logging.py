"""
Logging utilities for the video cleaner application.
"""

import logging
import os
from queue import Queue
from typing import Optional


class QueueHandler(logging.Handler):
    """Custom logging handler that puts log records into a queue for GUI display."""
    
    def __init__(self, log_queue: Queue):
        super().__init__()
        self.log_queue = log_queue
    
    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record to the queue."""
        try:
            self.log_queue.put(record)
        except Exception:
            # Silently ignore queue errors to prevent logging loops
            pass


def setup_logging(log_file_path: str, level: int = logging.INFO, 
                 log_queue: Optional[Queue] = None) -> logging.Logger:
    """
    Configure logging to file, console, and optionally to a queue for GUI display.
    
    Args:
        log_file_path: Path to the log file
        level: Logging level
        log_queue: Optional queue for GUI log display
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(__name__.split('.')[0])  # Get root package logger
    logger.setLevel(level)
    
    # Clear existing handlers to prevent duplicates
    if logger.hasHandlers():
        logger.handlers.clear()
    
    # Create log directory if it doesn't exist
    log_dir = os.path.dirname(log_file_path)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except OSError as e:
            print(f"Warning: Could not create log directory '{log_dir}'. "
                  f"Logging to current directory. Error: {e}")
            log_file_path = os.path.basename(log_file_path)
    
    # File handler
    try:
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not create file handler for {log_file_path}: {e}")
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Queue handler for GUI (if provided)
    if log_queue:
        queue_handler = QueueHandler(log_queue)
        queue_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        queue_handler.setFormatter(queue_formatter)
        logger.addHandler(queue_handler)
    
    return logger


def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger instance.
    
    Args:
        name: Logger name (defaults to package name)
        
    Returns:
        Logger instance
    """
    if name is None:
        name = __name__.split('.')[0]
    return logging.getLogger(name)
