"""
Video Cleaner - AI-Powered Foul Word Muter

A modular application for automatically detecting and muting foul words in videos
using AI transcription and optional subtitle verification.
"""

__version__ = "2.0.0"
__author__ = "Video Cleaner Team"

from .core.config import ProcessingConfig
from .core.audio_processor import extract_audio
from .core.video_processor import create_muted_video
from .core.transcription import transcribe_audio
from .core.subtitle_parser import parse_subtitles
from .gui import VideoCleanerGUI, run_gui

__all__ = [
    "ProcessingConfig",
    "extract_audio", 
    "create_muted_video",
    "transcribe_audio",
    "parse_subtitles",
    "VideoCleanerGUI",
    "run_gui",
]
