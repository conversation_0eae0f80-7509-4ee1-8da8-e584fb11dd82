"""
Helper utilities for error handling, resource management, and common operations.
"""

from functools import wraps
import time
import tempfile
import shutil
import logging
import os
from contextlib import contextmanager
from typing import Callable, Any, Generator, Tuple
import moviepy.editor as mp

logger = logging.getLogger(__name__)


def retry_on_failure(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry failed operations.
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay after each failed attempt
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            attempt = 1
            current_delay = delay
            
            while attempt <= max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {e}")
                        raise
                    
                    logger.warning(f"Attempt {attempt} of {func.__name__} failed: {e}. Retrying in {current_delay}s...")
                    time.sleep(current_delay)
                    current_delay *= backoff
                    attempt += 1
            
        return wrapper
    return decorator


@contextmanager
def temporary_directory() -> Generator[str, None, None]:
    """
    Create a temporary directory and clean it up afterwards.
    
    Yields:
        str: Path to the temporary directory
    """
    temp_dir = tempfile.mkdtemp()
    try:
        logger.debug(f"Created temporary directory: {temp_dir}")
        yield temp_dir
    finally:
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
            logger.debug(f"Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory {temp_dir}: {e}")


@contextmanager
def video_clip_manager(video_path: str) -> Generator[mp.VideoFileClip, None, None]:
    """
    Safely manage video clip resources.
    
    Args:
        video_path: Path to the video file
        
    Yields:
        VideoFileClip: The loaded video clip
    """
    clip = None
    try:
        logger.debug(f"Loading video clip: {video_path}")
        clip = mp.VideoFileClip(video_path)
        yield clip
    finally:
        if clip:
            try:
                clip.close()
                logger.debug(f"Closed video clip: {video_path}")
            except Exception as e:
                logger.warning(f"Error closing video clip {video_path}: {e}")


def validate_file_path(filepath: str, must_exist: bool = True) -> Tuple[bool, str]:
    """
    Validate a file path.
    
    Args:
        filepath: Path to validate
        must_exist: Whether the file must already exist
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not filepath:
        return False, "File path is empty"
    
    if must_exist and not os.path.exists(filepath):
        return False, f"File does not exist: {filepath}"
    
    if must_exist and not os.path.isfile(filepath):
        return False, f"Path is not a file: {filepath}"
    
    return True, ""


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def safe_filename(filename: str) -> str:
    """
    Make a filename safe for the filesystem.
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename with invalid characters replaced
    """
    import re
    # Replace invalid characters with underscores
    safe = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple consecutive underscores
    safe = re.sub(r'_+', '_', safe)
    # Remove leading/trailing underscores and dots
    safe = safe.strip('_.')
    return safe or "unnamed"
