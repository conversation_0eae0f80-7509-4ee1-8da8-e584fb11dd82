#!/usr/bin/env python3
"""
Test script to verify file validation in the Video Cleaner application.
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Add the code directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'code'))

from video_cleaner import VideoCleanerGUI
import tkinter as tk


class TestFileValidation(unittest.TestCase):
    """Test file validation functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide the window during testing
        self.app = VideoCleanerGUI(self.root)
        
        # Create temporary files for testing
        self.temp_dir = tempfile.mkdtemp()
        self.test_video = os.path.join(self.temp_dir, "test_video.mp4")
        self.test_words = os.path.join(self.temp_dir, "test_words.txt")
        self.test_output = os.path.join(self.temp_dir, "output.mp4")
        
        # Create test files
        with open(self.test_video, 'w') as f:
            f.write("fake video content")
        with open(self.test_words, 'w') as f:
            f.write("damn\nhell\ncrap")
    
    def tearDown(self):
        """Clean up test environment."""
        self.root.destroy()
        # Clean up temp files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_empty_video_path(self):
        """Test validation with empty video path."""
        self.app.video_path.set("")
        self.app.words_file.set(self.test_words)
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertFalse(result)
            mock_error.assert_called_once()
            args = mock_error.call_args[0]
            self.assertEqual(args[0], "Error")
            self.assertIn("select a video file", args[1])
    
    def test_nonexistent_video_file(self):
        """Test validation with non-existent video file."""
        self.app.video_path.set("nonexistent_video.mp4")
        self.app.words_file.set(self.test_words)
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertFalse(result)
            mock_error.assert_called_once()
            args = mock_error.call_args[0]
            self.assertEqual(args[0], "Error")
            self.assertIn("does not exist", args[1])
    
    def test_empty_words_file(self):
        """Test validation with empty words file path."""
        self.app.video_path.set(self.test_video)
        self.app.words_file.set("")
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertFalse(result)
            mock_error.assert_called_once()
            args = mock_error.call_args[0]
            self.assertEqual(args[0], "Error")
            self.assertIn("select a foul words file", args[1])
    
    def test_nonexistent_words_file(self):
        """Test validation with non-existent words file."""
        self.app.video_path.set(self.test_video)
        self.app.words_file.set("nonexistent_words.txt")
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertFalse(result)
            mock_error.assert_called_once()
            args = mock_error.call_args[0]
            self.assertEqual(args[0], "Error")
            self.assertIn("does not exist", args[1])
    
    def test_empty_output_path(self):
        """Test validation with empty output path."""
        self.app.video_path.set(self.test_video)
        self.app.words_file.set(self.test_words)
        self.app.output_path.set("")
        
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertFalse(result)
            mock_error.assert_called_once()
            args = mock_error.call_args[0]
            self.assertEqual(args[0], "Error")
            self.assertIn("specify an output file path", args[1])
    
    def test_valid_inputs(self):
        """Test validation with all valid inputs."""
        self.app.video_path.set(self.test_video)
        self.app.words_file.set(self.test_words)
        self.app.output_path.set(self.test_output)
        
        # Mock the messagebox for overwrite confirmation
        with patch('tkinter.messagebox.showerror') as mock_error:
            result = self.app.validate_inputs()
            self.assertTrue(result)
            mock_error.assert_not_called()
    
    def test_existing_output_file_overwrite_yes(self):
        """Test validation with existing output file - user says yes to overwrite."""
        # Create the output file
        with open(self.test_output, 'w') as f:
            f.write("existing content")
        
        self.app.video_path.set(self.test_video)
        self.app.words_file.set(self.test_words)
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.askyesno', return_value=True):
            result = self.app.validate_inputs()
            self.assertTrue(result)
    
    def test_existing_output_file_overwrite_no(self):
        """Test validation with existing output file - user says no to overwrite."""
        # Create the output file
        with open(self.test_output, 'w') as f:
            f.write("existing content")
        
        self.app.video_path.set(self.test_video)
        self.app.words_file.set(self.test_words)
        self.app.output_path.set(self.test_output)
        
        with patch('tkinter.messagebox.askyesno', return_value=False):
            result = self.app.validate_inputs()
            self.assertFalse(result)


if __name__ == '__main__':
    print("Testing file validation functionality...")
    unittest.main(verbosity=2)
