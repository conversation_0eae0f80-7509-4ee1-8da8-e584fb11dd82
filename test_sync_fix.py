#!/usr/bin/env python3
"""
Test script to verify audio-video synchronization fixes in Video Cleaner.
This script creates a test video and verifies that sync is maintained after processing.
"""

import os
import sys
import time
import tempfile
import moviepy.editor as mp
import numpy as np
from pathlib import Path

# Add the code directory to the path so we can import video_cleaner
sys.path.insert(0, 'code')

def create_test_video(output_path: str, duration: float = 10.0) -> bool:
    """
    Create a simple test video with synchronized audio and visual cues.

    Args:
        output_path: Path where to save the test video
        duration: Duration of the test video in seconds

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Creating test video: {output_path}")

        # Create a simple video with a moving rectangle and synchronized beeps
        fps = 24
        width, height = 640, 480

        def make_frame(t):
            """Create a frame with a moving rectangle and timestamp"""
            frame = np.zeros((height, width, 3), dtype=np.uint8)

            # Add a moving rectangle
            rect_x = int((t / duration) * (width - 100))
            rect_y = height // 2 - 25
            frame[rect_y:rect_y+50, rect_x:rect_x+100] = [255, 255, 255]  # White rectangle

            # Add timestamp text overlay (simulated with a colored bar)
            bar_height = int((t / duration) * height)
            frame[0:bar_height, 0:20] = [0, 255, 0]  # Green progress bar

            return frame

        # Create video clip
        video_clip = mp.VideoClip(make_frame, duration=duration).set_fps(fps)

        # Create synchronized audio with beeps every 2 seconds
        def make_audio(t):
            """Create audio with beeps every 2 seconds"""
            # Generate a sine wave beep every 2 seconds
            beep_interval = 2.0
            beep_duration = 0.1
            frequency = 440  # A4 note

            # Handle both scalar and array inputs
            if hasattr(t, '__len__'):
                # Array input
                result = np.zeros_like(t)
                time_in_cycle = t % beep_interval
                beep_mask = time_in_cycle < beep_duration
                result[beep_mask] = np.sin(2 * np.pi * frequency * t[beep_mask]) * 0.3
                return result
            else:
                # Scalar input
                time_in_cycle = t % beep_interval
                if time_in_cycle < beep_duration:
                    return np.sin(2 * np.pi * frequency * t) * 0.3
                else:
                    return 0.0

        # Create audio clip
        audio_clip = mp.AudioClip(make_audio, duration=duration, fps=44100)

        # Combine video and audio
        final_clip = video_clip.set_audio(audio_clip)

        # Write the test video
        final_clip.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            fps=fps,
            logger=None  # Suppress output
        )

        # Clean up
        video_clip.close()
        audio_clip.close()
        final_clip.close()

        print(f"Test video created successfully: {output_path}")
        return True

    except Exception as e:
        print(f"Error creating test video: {e}")
        return False

def create_test_foul_words_file(file_path: str) -> bool:
    """Create a test foul words file with words that will trigger at specific times."""
    try:
        # We'll use common words that might appear in our test
        test_words = ["test", "beep", "sync", "audio", "video"]

        with open(file_path, 'w') as f:
            for word in test_words:
                f.write(f"{word}\n")

        print(f"Test foul words file created: {file_path}")
        return True

    except Exception as e:
        print(f"Error creating foul words file: {e}")
        return False

def verify_sync(original_path: str, processed_path: str) -> bool:
    """
    Verify that the processed video maintains synchronization.

    Args:
        original_path: Path to original video
        processed_path: Path to processed video

    Returns:
        True if sync is maintained, False otherwise
    """
    try:
        print("Verifying synchronization...")

        # Load both videos
        original = mp.VideoFileClip(original_path)
        processed = mp.VideoFileClip(processed_path)

        # Check basic properties
        duration_diff = abs(original.duration - processed.duration)
        fps_diff = abs(original.fps - processed.fps)

        print(f"Original duration: {original.duration:.3f}s")
        print(f"Processed duration: {processed.duration:.3f}s")
        print(f"Duration difference: {duration_diff:.3f}s")
        print(f"FPS difference: {fps_diff:.3f}")

        # Check if durations match within tolerance (10ms)
        duration_ok = duration_diff < 0.01
        fps_ok = fps_diff < 0.1

        # Check audio properties if available
        audio_ok = True
        if original.audio and processed.audio:
            audio_duration_diff = abs(original.audio.duration - processed.audio.duration)
            print(f"Audio duration difference: {audio_duration_diff:.3f}s")
            audio_ok = audio_duration_diff < 0.01

        # Clean up
        original.close()
        processed.close()

        sync_maintained = duration_ok and fps_ok and audio_ok

        if sync_maintained:
            print("✅ Synchronization verification PASSED")
        else:
            print("❌ Synchronization verification FAILED")
            if not duration_ok:
                print(f"  - Duration mismatch: {duration_diff:.3f}s")
            if not fps_ok:
                print(f"  - FPS mismatch: {fps_diff:.3f}")
            if not audio_ok:
                print(f"  - Audio duration mismatch")

        return sync_maintained

    except Exception as e:
        print(f"Error verifying sync: {e}")
        return False

def main():
    """Main test function."""
    print("🎬 Video Cleaner Synchronization Test")
    print("=" * 50)

    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # File paths
        test_video = temp_path / "test_video.mp4"
        test_words = temp_path / "test_words.txt"
        output_video = temp_path / "test_video_muted_ai.mp4"

        print(f"Test directory: {temp_dir}")

        # Step 1: Create test video
        print("\n1. Creating test video...")
        if not create_test_video(str(test_video)):
            print("❌ Failed to create test video")
            return False

        # Step 2: Create test foul words file
        print("\n2. Creating test foul words file...")
        if not create_test_foul_words_file(str(test_words)):
            print("❌ Failed to create test foul words file")
            return False

        # Step 3: Process video with Video Cleaner (AI-only mode)
        print("\n3. Processing video with Video Cleaner...")
        try:
            # Import and run video cleaner
            from video_cleaner import main as video_cleaner_main

            # Temporarily modify sys.argv to simulate command line arguments
            original_argv = sys.argv.copy()
            sys.argv = [
                'video_cleaner.py',
                str(test_video),  # video path
                '-w', str(test_words),  # words file
                '-o', str(output_video),  # output path
                '--log_level', 'WARNING'  # Reduce log noise
            ]

            # Run video cleaner
            video_cleaner_main()

            # Restore original argv
            sys.argv = original_argv

        except SystemExit as e:
            # video_cleaner calls sys.exit(), which is expected
            if e.code != 0:
                print(f"❌ Video Cleaner failed with exit code: {e.code}")
                return False
        except Exception as e:
            print(f"❌ Error running Video Cleaner: {e}")
            return False

        # Step 4: Verify output exists
        if not output_video.exists():
            print("❌ Output video was not created")
            return False

        print("✅ Video processing completed")

        # Step 5: Verify synchronization
        print("\n4. Verifying synchronization...")
        sync_ok = verify_sync(str(test_video), str(output_video))

        # Final result
        print("\n" + "=" * 50)
        if sync_ok:
            print("🎉 SYNCHRONIZATION TEST PASSED!")
            print("The Video Cleaner maintains perfect audio-video sync.")
        else:
            print("💥 SYNCHRONIZATION TEST FAILED!")
            print("Audio-video sync issues detected.")

        return sync_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
