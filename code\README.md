# 🎬 Video Cleaner v2.0 - AI-Powered Foul Word Muter

An advanced, modular application for automatically detecting and muting foul words in videos using AI transcription with optional subtitle verification.

## ✨ What's New in v2.0

### 🏗️ **Complete Architecture Overhaul**
- **Modular Design**: Separated GUI from core logic with clean package structure
- **Configuration Management**: Centralized settings with save/load functionality
- **Data Classes**: Structured return types for better type safety and clarity
- **Performance Optimizations**: Memory monitoring, chunked processing, and model caching

### 🛡️ **Enhanced Error Handling & Robustness**
- **Retry Logic**: Automatic retry for failed operations with exponential backoff
- **Resource Management**: Context managers for safe file and video clip handling
- **Input Validation**: Comprehensive validation for all file types and parameters
- **Cleanup System**: Automatic cleanup of temporary files and resources

### 🚀 **Performance Improvements**
- **Memory Monitoring**: Real-time memory usage tracking with automatic cleanup
- **Whisper Model Caching**: Avoid reloading models between operations
- **Progress Callbacks**: Detailed progress reporting for long operations
- **Chunked Processing**: Handle large videos without memory issues

### 🎯 **New Features**
- **Batch Processing**: Process multiple videos in one operation
- **Export/Import**: Export detection results to CSV/JSON, import word lists
- **Preview Generation**: Create preview clips of mute segments for review
- **Settings Management**: Save and load custom configurations
- **Comprehensive Testing**: Full unit test suite with 54+ test cases

### 🐛 **Bug Fixes**
- Fixed memory leaks in GUI log monitoring
- Improved audio-video synchronization
- Better handling of edge cases and error conditions
- Enhanced cleanup on application exit

## 📁 Project Structure

```
video_cleaner/
├── __init__.py                 # Package initialization
├── main.py                     # Main entry point
├── core/                       # Core processing logic
│   ├── __init__.py
│   ├── config.py              # Configuration management
│   ├── data_classes.py        # Structured data types
│   ├── audio_processor.py     # Audio extraction
│   ├── video_processor.py     # Video processing & muting
│   ├── transcription.py       # AI transcription with Whisper
│   └── subtitle_parser.py     # SRT subtitle parsing
├── gui/                        # User interface
│   ├── __init__.py
│   ├── main_window.py         # Main GUI application
│   └── batch_processing.py    # Batch processing dialog
└── utils/                      # Utility modules
    ├── __init__.py
    ├── logging.py             # Logging utilities
    ├── helpers.py             # Helper functions
    ├── validation.py          # Input validation
    ├── cleanup.py             # Resource cleanup
    ├── performance.py         # Performance monitoring
    ├── export_import.py       # Export/import utilities
    └── preview.py             # Preview generation
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- FFmpeg (for video processing)
- Required Python packages (install via pip)

### Installation
```bash
# Clone or download the project
cd video_cleaner

# Install dependencies
pip install -r requirements.txt

# Run the application
python video_cleaner.py --gui
```

### Basic Usage

#### GUI Mode (Recommended)
```bash
python video_cleaner.py --gui
```

#### Command Line Mode
```bash
# With subtitle verification
python video_cleaner.py input_video.mp4 subtitles.srt -w foul_words.txt

# AI-only mode (no subtitles)
python video_cleaner.py input_video.mp4 -w foul_words.txt

# Custom output path
python video_cleaner.py input_video.mp4 -w foul_words.txt -o cleaned_video.mp4
```

## 🎛️ Configuration

### Configuration File
Create a `config.json` file to customize settings:

```json
{
  "model_size": "medium",
  "mute_padding_start": 0.05,
  "mute_padding_end": 0.05,
  "video_crf": "18",
  "video_preset": "medium",
  "max_video_size_gb": 10.0,
  "max_retry_attempts": 3
}
```

### Available Models
- **tiny**: Fastest, least accurate
- **base**: Good balance of speed and accuracy
- **small**: Better accuracy, slower
- **medium**: Recommended default
- **large**: Best accuracy, slowest

## 🔧 Advanced Features

### Batch Processing
Process multiple videos at once:
1. Open GUI mode
2. Click "📦 Batch Processing"
3. Add videos and configure settings
4. Start batch processing

### Export Results
Export detection results for analysis:
```python
from video_cleaner.utils.export_import import export_detected_words_csv
export_detected_words_csv(results, "detected_words.csv")
```

### Preview Generation
Generate preview clips of mute segments:
```python
from video_cleaner.utils.preview import generate_preview_clip
preview_path = generate_preview_clip(video_path, (start, end))
```

### Custom Word Lists
Import word lists from various formats:
```python
from video_cleaner.utils.export_import import import_word_list_csv
words = import_word_list_csv("custom_words.csv", word_column="word")
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
# Run all tests
python run_tests.py

# Run specific test module
python run_tests.py config
python run_tests.py validation
```

## 📊 Performance Monitoring

The application includes built-in performance monitoring:
- Memory usage tracking
- Processing time measurement
- Automatic cleanup when memory usage is high
- Progress reporting for long operations

## 🛠️ Development

### Adding New Features
1. Follow the modular architecture
2. Add appropriate unit tests
3. Update documentation
4. Use the provided data classes for structured returns

### Code Quality
- Type hints throughout the codebase
- Comprehensive error handling
- Resource cleanup with context managers
- Logging for debugging and monitoring

## 📝 Processing Modes

### 1. Subtitle Verification Mode
- Uses AI transcription + subtitle verification
- Higher accuracy, fewer false positives
- Requires SRT subtitle file
- Recommended for final production

### 2. AI-Only Mode
- Uses only AI transcription
- Faster processing
- May have more false positives
- Good for initial screening

## 🔍 Troubleshooting

### Common Issues

**Memory Issues with Large Videos**
- Enable chunked processing (automatic for videos >5 minutes)
- Reduce video quality settings
- Close other applications

**Audio Sync Issues**
- Ensure FFmpeg is properly installed
- Check video file integrity
- Try different video codec settings

**Transcription Accuracy**
- Use larger Whisper models for better accuracy
- Ensure good audio quality in source video
- Consider using subtitle verification mode

### Logging
Detailed logs are saved to `[video_name]_processing.log` for debugging.

## 🤝 Contributing

1. Follow the existing code structure
2. Add unit tests for new features
3. Update documentation
4. Ensure all tests pass

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI Whisper for AI transcription
- MoviePy for video processing
- The open-source community for various utilities

---

**Note**: This application is designed for content moderation and educational purposes. Always review results before using in production environments.
