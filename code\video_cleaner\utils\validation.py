"""
Input validation utilities for the video cleaner application.
"""

import os
import mimetypes
import logging
from pathlib import Path
from typing import List, Tuple, Optional
import moviepy.editor as mp

from ..core.data_classes import ValidationResult, FileInfo
from ..core.config import get_config

logger = logging.getLogger(__name__)


def validate_video_file(video_path: str) -> ValidationResult:
    """
    Comprehensive validation of video file.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        ValidationResult with validation status and details
    """
    result = ValidationResult(is_valid=True)
    
    if not video_path:
        result.add_error("Video path is empty")
        return result
    
    path = Path(video_path)
    
    # Check existence
    if not path.exists():
        result.add_error(f"Video file does not exist: {video_path}")
        return result
    
    if not path.is_file():
        result.add_error(f"Path is not a file: {video_path}")
        return result
    
    # Check file size
    try:
        size_bytes = path.stat().st_size
        config = get_config()
        max_size_bytes = config.max_video_size_gb * 1024 * 1024 * 1024
        
        if size_bytes == 0:
            result.add_error("Video file is empty")
            return result
        
        if size_bytes > max_size_bytes:
            size_gb = size_bytes / (1024 * 1024 * 1024)
            result.add_error(f"Video file too large ({size_gb:.2f} GB > {config.max_video_size_gb} GB)")
            return result
            
    except OSError as e:
        result.add_error(f"Error checking file size: {e}")
        return result
    
    # Check MIME type
    mime_type, _ = mimetypes.guess_type(str(path))
    if mime_type and not mime_type.startswith('video/'):
        result.add_warning(f"File does not appear to be a video (MIME type: {mime_type})")
    
    # Check file extension
    valid_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    if path.suffix.lower() not in valid_extensions:
        result.add_warning(f"Unusual video file extension: {path.suffix}")
    
    # Try to open with moviepy for deeper validation
    try:
        with mp.VideoFileClip(str(path)) as clip:
            if clip.duration <= 0:
                result.add_error("Video has zero or negative duration")
                return result
            
            if clip.duration > 24 * 3600:  # 24 hours
                result.add_warning(f"Very long video ({clip.duration/3600:.1f} hours)")
            
            if clip.audio is None:
                result.add_error("Video has no audio track (required for processing)")
                return result
                
    except Exception as e:
        result.add_error(f"Cannot read video file: {e}")
        return result
    
    return result


def validate_subtitle_file(srt_path: str) -> ValidationResult:
    """
    Validate subtitle file.
    
    Args:
        srt_path: Path to the subtitle file
        
    Returns:
        ValidationResult with validation status and details
    """
    result = ValidationResult(is_valid=True)
    
    if not srt_path:
        # Subtitle file is optional
        return result
    
    path = Path(srt_path)
    
    # Check existence
    if not path.exists():
        result.add_error(f"Subtitle file does not exist: {srt_path}")
        return result
    
    if not path.is_file():
        result.add_error(f"Path is not a file: {srt_path}")
        return result
    
    # Check file size
    try:
        size_bytes = path.stat().st_size
        max_size = 50 * 1024 * 1024  # 50MB limit for subtitle files
        
        if size_bytes == 0:
            result.add_error("Subtitle file is empty")
            return result
        
        if size_bytes > max_size:
            result.add_error(f"Subtitle file too large ({size_bytes} bytes > {max_size})")
            return result
            
    except OSError as e:
        result.add_error(f"Error checking subtitle file size: {e}")
        return result
    
    # Check file extension
    if path.suffix.lower() not in ['.srt', '.txt']:
        result.add_warning(f"Unusual subtitle file extension: {path.suffix}")
    
    # Try to read first few lines to check format
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            first_lines = [f.readline().strip() for _ in range(5)]
        
        # Basic SRT format check - first line should be a number
        if first_lines and first_lines[0] and not first_lines[0].isdigit():
            result.add_warning("File does not appear to be in SRT format (first line should be a number)")
            
    except UnicodeDecodeError:
        # Try with different encoding
        try:
            with open(srt_path, 'r', encoding='latin1') as f:
                first_lines = [f.readline().strip() for _ in range(5)]
        except Exception as e:
            result.add_error(f"Could not read subtitle file: {e}")
            return result
    except Exception as e:
        result.add_error(f"Error reading subtitle file: {e}")
        return result
    
    return result


def validate_words_file(words_path: str) -> ValidationResult:
    """
    Validate foul words file.
    
    Args:
        words_path: Path to the words file
        
    Returns:
        ValidationResult with validation status and details
    """
    result = ValidationResult(is_valid=True)
    
    if not words_path:
        result.add_error("Words file path is empty")
        return result
    
    path = Path(words_path)
    
    # Check existence
    if not path.exists():
        result.add_error(f"Words file does not exist: {words_path}")
        return result
    
    if not path.is_file():
        result.add_error(f"Path is not a file: {words_path}")
        return result
    
    # Check file size
    try:
        size_bytes = path.stat().st_size
        
        if size_bytes == 0:
            result.add_warning("Words file is empty - no words will be muted")
            return result
        
        if size_bytes > 1024 * 1024:  # 1MB limit
            result.add_warning(f"Words file is very large ({size_bytes} bytes)")
            
    except OSError as e:
        result.add_error(f"Error checking words file size: {e}")
        return result
    
    # Try to read and validate content
    try:
        with open(words_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        words = [line.strip() for line in lines if line.strip()]
        
        if not words:
            result.add_warning("Words file contains no valid words")
        elif len(words) > 10000:
            result.add_warning(f"Very large word list ({len(words)} words) may slow processing")
        
        # Check for suspicious content
        very_long_words = [w for w in words if len(w) > 50]
        if very_long_words:
            result.add_warning(f"Found {len(very_long_words)} unusually long words (>50 characters)")
            
    except UnicodeDecodeError:
        result.add_error("Could not read words file - encoding issue")
        return result
    except Exception as e:
        result.add_error(f"Error reading words file: {e}")
        return result
    
    return result


def validate_output_path(output_path: str, input_path: str = "") -> ValidationResult:
    """
    Validate output file path.
    
    Args:
        output_path: Path where output will be saved
        input_path: Input file path for reference
        
    Returns:
        ValidationResult with validation status and details
    """
    result = ValidationResult(is_valid=True)
    
    if not output_path:
        result.add_error("Output path is empty")
        return result
    
    path = Path(output_path)
    
    # Check if output directory exists or can be created
    output_dir = path.parent
    if not output_dir.exists():
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            result.add_error(f"Cannot create output directory: {e}")
            return result
    
    # Check if we can write to the directory
    if not os.access(output_dir, os.W_OK):
        result.add_error(f"No write permission for output directory: {output_dir}")
        return result
    
    # Check if output file already exists
    if path.exists():
        result.add_warning(f"Output file already exists and will be overwritten: {output_path}")
    
    # Check if output path is the same as input path
    if input_path and Path(input_path).resolve() == path.resolve():
        result.add_error("Output path cannot be the same as input path")
        return result
    
    # Check file extension
    if input_path:
        input_ext = Path(input_path).suffix.lower()
        output_ext = path.suffix.lower()
        
        if output_ext != input_ext:
            result.add_warning(f"Output extension ({output_ext}) differs from input ({input_ext})")
    
    return result


def validate_all_inputs(video_path: str, srt_path: str = "", words_path: str = "", 
                       output_path: str = "") -> ValidationResult:
    """
    Validate all input parameters together.
    
    Args:
        video_path: Path to video file
        srt_path: Path to subtitle file (optional)
        words_path: Path to words file
        output_path: Path for output file
        
    Returns:
        Combined validation result
    """
    combined_result = ValidationResult(is_valid=True)
    
    # Validate each component
    video_result = validate_video_file(video_path)
    subtitle_result = validate_subtitle_file(srt_path) if srt_path else ValidationResult(is_valid=True)
    words_result = validate_words_file(words_path)
    output_result = validate_output_path(output_path, video_path)
    
    # Combine results
    if not video_result.is_valid:
        combined_result.is_valid = False
        combined_result.error_message = video_result.error_message
    elif not words_result.is_valid:
        combined_result.is_valid = False
        combined_result.error_message = words_result.error_message
    elif not subtitle_result.is_valid:
        combined_result.is_valid = False
        combined_result.error_message = subtitle_result.error_message
    elif not output_result.is_valid:
        combined_result.is_valid = False
        combined_result.error_message = output_result.error_message
    
    # Combine warnings
    all_warnings = (video_result.warnings + subtitle_result.warnings + 
                   words_result.warnings + output_result.warnings)
    combined_result.warnings.extend(all_warnings)
    
    return combined_result
