#!/usr/bin/env python3
"""
Test runner for Video Cleaner application.
"""

import sys
import os
import unittest
import time
from pathlib import Path

# Add the current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def discover_and_run_tests():
    """Discover and run all tests."""
    print("🧪 Video Cleaner Test Suite")
    print("=" * 50)
    
    # Discover tests
    test_dir = Path(__file__).parent / "tests"
    loader = unittest.TestLoader()
    
    try:
        suite = loader.discover(str(test_dir), pattern="test_*.py")
        
        # Count tests
        test_count = suite.countTestCases()
        print(f"📊 Discovered {test_count} test cases")
        print()
        
        # Run tests
        runner = unittest.TextTestRunner(
            verbosity=2,
            stream=sys.stdout,
            buffer=True
        )
        
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📋 Test Summary")
        print(f"⏱️  Total time: {end_time - start_time:.2f} seconds")
        print(f"✅ Tests run: {result.testsRun}")
        print(f"❌ Failures: {len(result.failures)}")
        print(f"💥 Errors: {len(result.errors)}")
        print(f"⏭️  Skipped: {len(result.skipped)}")
        
        # Print details for failures and errors
        if result.failures:
            print("\n🔴 Failures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
        
        if result.errors:
            print("\n💥 Errors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
        
        # Overall result
        if result.wasSuccessful():
            print("\n🎉 All tests passed!")
            return 0
        else:
            print(f"\n⚠️  {len(result.failures + result.errors)} test(s) failed")
            return 1
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


def run_specific_test(test_module):
    """Run a specific test module."""
    print(f"🧪 Running specific test: {test_module}")
    print("=" * 50)
    
    try:
        # Import the test module
        module = __import__(f"tests.{test_module}", fromlist=[test_module])
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return 0 if result.wasSuccessful() else 1
        
    except ImportError as e:
        print(f"❌ Could not import test module '{test_module}': {e}")
        return 1
    except Exception as e:
        print(f"❌ Error running test '{test_module}': {e}")
        return 1


def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        # Run specific test
        test_module = sys.argv[1]
        if test_module.startswith("test_"):
            test_module = test_module[5:]  # Remove "test_" prefix
        if test_module.endswith(".py"):
            test_module = test_module[:-3]  # Remove ".py" suffix
        
        return run_specific_test(f"test_{test_module}")
    else:
        # Run all tests
        return discover_and_run_tests()


if __name__ == "__main__":
    sys.exit(main())
