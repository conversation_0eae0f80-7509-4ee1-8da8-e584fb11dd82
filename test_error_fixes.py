#!/usr/bin/env python3
"""
Test script to verify the error fixes in the Video Cleaner application.
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# Add the code directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'code'))

from video_cleaner import create_muted_video
import logging

# Set up logging for testing
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class TestErrorFixes(unittest.TestCase):
    """Test error handling and fixes in the Video Cleaner."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_extension_handling(self):
        """Test that file extensions are handled correctly."""
        # Test with .mkv extension
        mkv_output = os.path.join(self.temp_dir, "test_output.mkv")
        base, ext = os.path.splitext(mkv_output)
        temp_video = f"{base}_temp_video{ext}"
        temp_audio = f"{base}_temp_audio.wav"
        
        self.assertEqual(temp_video, os.path.join(self.temp_dir, "test_output_temp_video.mkv"))
        self.assertEqual(temp_audio, os.path.join(self.temp_dir, "test_output_temp_audio.wav"))
        
        # Test with .mp4 extension
        mp4_output = os.path.join(self.temp_dir, "test_output.mp4")
        base, ext = os.path.splitext(mp4_output)
        temp_video = f"{base}_temp_video{ext}"
        temp_audio = f"{base}_temp_audio.wav"
        
        self.assertEqual(temp_video, os.path.join(self.temp_dir, "test_output_temp_video.mp4"))
        self.assertEqual(temp_audio, os.path.join(self.temp_dir, "test_output_temp_audio.wav"))
    
    def test_error_handling_with_missing_files(self):
        """Test error handling when input files are missing."""
        nonexistent_video = os.path.join(self.temp_dir, "nonexistent.mkv")
        output_path = os.path.join(self.temp_dir, "output.mkv")
        mute_segments = [(1.0, 2.0), (5.0, 6.0)]
        
        # This should return False due to missing input file
        result = create_muted_video(nonexistent_video, output_path, mute_segments)
        self.assertFalse(result)
    
    def test_output_path_validation(self):
        """Test output path validation."""
        # Test with various file extensions
        test_cases = [
            "output.mp4",
            "output.mkv", 
            "output.avi",
            "output.mov"
        ]
        
        for filename in test_cases:
            output_path = os.path.join(self.temp_dir, filename)
            base, ext = os.path.splitext(output_path)
            
            # Verify temp file naming works correctly
            temp_video = f"{base}_temp_video{ext}"
            temp_audio = f"{base}_temp_audio.wav"
            
            self.assertTrue(temp_video.endswith(ext))
            self.assertTrue(temp_audio.endswith(".wav"))
            self.assertIn("_temp_video", temp_video)
            self.assertIn("_temp_audio", temp_audio)
    
    def test_mute_segments_validation(self):
        """Test mute segments validation."""
        # Test empty mute segments
        empty_segments = []
        self.assertEqual(len(empty_segments), 0)
        
        # Test valid mute segments
        valid_segments = [(1.0, 2.0), (5.0, 6.0), (10.0, 12.0)]
        self.assertEqual(len(valid_segments), 3)
        
        # Test segment ordering
        for i, (start, end) in enumerate(valid_segments):
            self.assertLess(start, end, f"Segment {i}: start time should be less than end time")
    
    def test_logging_configuration(self):
        """Test that logging is properly configured."""
        # Test that logger exists and is configured
        test_logger = logging.getLogger('video_cleaner')
        self.assertIsNotNone(test_logger)
        
        # Test log levels
        log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        for level_name in log_levels:
            level = getattr(logging, level_name)
            self.assertIsInstance(level, int)
    
    def test_duration_calculation(self):
        """Test duration calculation and validation."""
        # Test duration calculations
        test_segments = [
            (1.0, 2.0),    # 1 second
            (5.0, 6.5),    # 1.5 seconds  
            (10.0, 12.5)   # 2.5 seconds
        ]
        
        total_muted_duration = sum(end - start for start, end in test_segments)
        expected_duration = 1.0 + 1.5 + 2.5  # 5.0 seconds
        
        self.assertEqual(total_muted_duration, expected_duration)
    
    def test_file_path_normalization(self):
        """Test file path normalization."""
        # Test various path formats
        test_paths = [
            "video.mkv",
            "./video.mkv",
            os.path.join(self.temp_dir, "video.mkv"),
            "C:\\Users\\<USER>\\video.mkv" if os.name == 'nt' else "/home/<USER>/video.mkv"
        ]
        
        for path in test_paths:
            # Test that os.path.splitext works correctly
            base, ext = os.path.splitext(path)
            self.assertTrue(ext in ['.mkv', '.mp4', '.avi', '.mov', ''])
            
            # Test temp file generation
            if ext:
                temp_video = f"{base}_temp_video{ext}"
                temp_audio = f"{base}_temp_audio.wav"
                
                self.assertTrue(temp_video.endswith(ext))
                self.assertTrue(temp_audio.endswith('.wav'))


if __name__ == '__main__':
    print("Testing error fixes and file handling...")
    print(f"Python version: {sys.version}")
    print(f"Operating system: {os.name}")
    print(f"Platform: {sys.platform}")
    print("-" * 50)
    
    unittest.main(verbosity=2)
