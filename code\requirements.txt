# Core dependencies for Video Cleaner v2.0

# AI Transcription
openai-whisper>=20231117
stable-ts>=2.14.0
openai>=1.37.0

# Video and Audio Processing
# Pin to <2.0 because 2.x removed the `moviepy.editor` aggregator used across the codebase
moviepy<2.0
imageio>=2.25.0
imageio-ffmpeg>=0.4.8

# Subtitle Processing
srt>=3.5.3

# Data Processing
numpy>=1.21.0
pandas>=1.3.0

# System Monitoring
psutil>=5.9.0

# GUI (included with Python, but listed for completeness)
# tkinter - included with Python standard library

# Development and Testing (optional)
pytest>=7.0.0
pytest-cov>=4.0.0

# Optional: For better performance
# torch>=2.0.0  # Uncomment if you want GPU acceleration for Whisper
# torchaudio>=2.0.0  # Uncomment if you want GPU acceleration for Whisper
