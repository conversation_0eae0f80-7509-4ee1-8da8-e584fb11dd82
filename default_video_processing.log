2025-05-19 16:41:36,319 - INFO - video_cleaner_2 - banner - 
------------ Initial Setup Complete & Logging Finalized ------------
2025-05-19 16:41:36,320 - INFO - video_cleaner_2 - main - Full configuration: {'video_path': WindowsPath('C:/Users/<USER>/OneDrive/Documents/Video Editing/Video Cleaner/default_video.mp4'), 'srt_path': None, 'words_file': WindowsPath('foul_words.txt'), 'output_path': WindowsPath('C:/Users/<USER>/OneDrive/Documents/Video Editing/Video Cleaner/default_video_muted_verified.mp4'), 'model': 'medium', 'force_overwrite': False, 'initial_prompt': None, 'ffmpeg_preset': 'medium', 'threads': None, 'extra_ffmpeg_params': None, 'dry_run': False, 'generate_ffmpeg_commands': False, 'report_file': WindowsPath('C:/Users/<USER>/OneDrive/Documents/Video Editing/Video Cleaner/default_video_report.log'), 'log_file': WindowsPath('C:/Users/<USER>/OneDrive/Documents/Video Editing/Video Cleaner/default_video_processing.log'), 'log_level': 'INFO', 'keep_temp_files': False}
2025-05-19 16:41:36,321 - INFO - video_cleaner_2 - main - Simulating transcription and segment finding...
2025-05-19 16:41:36,321 - INFO - video_cleaner_2 - banner - 
------------ Creating Muted Video (custom FL method) for 'C:\Users\<USER>\OneDrive\Documents\Video Editing\Video Cleaner\default_video.mp4' ------------
2025-05-19 16:41:36,352 - ERROR - video_cleaner_2 - create_muted_video_volumex - Error creating final muted video using custom FL method: MoviePy error: the file C:\Users\<USER>\OneDrive\Documents\Video Editing\Video Cleaner\default_video.mp4 could not be found!
Please check that you entered the correct path.
Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive\Documents\Video Editing\Video Cleaner\video_cleaner_2.py", line 294, in create_muted_video_volumex
    video_clip = mp.VideoFileClip(str(video_path), verbose=False)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\moviepy\video\io\VideoFileClip.py", line 88, in __init__
    self.reader = FFMPEG_VideoReader(filename, pix_fmt=pix_fmt,
                  ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                     target_resolution=target_resolution,
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                     resize_algo=resize_algorithm,
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                     fps_source=fps_source)
                                     ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\moviepy\video\io\ffmpeg_reader.py", line 35, in __init__
    infos = ffmpeg_parse_infos(filename, print_infos, check_duration,
                               fps_source)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\moviepy\video\io\ffmpeg_reader.py", line 270, in ffmpeg_parse_infos
    raise IOError(("MoviePy error: the file %s could not be found!\n"
                  "Please check that you entered the correct "
                  "path.")%filename)
OSError: MoviePy error: the file C:\Users\<USER>\OneDrive\Documents\Video Editing\Video Cleaner\default_video.mp4 could not be found!
Please check that you entered the correct path.
2025-05-19 16:41:36,381 - CRITICAL - video_cleaner_2 - main - A critical error occurred: Failed to create the final muted video using custom FL method.
2025-05-19 16:41:36,381 - INFO - video_cleaner_2 - banner - 
------------ Cleanup ------------
2025-05-19 16:41:36,382 - INFO - video_cleaner_2 - banner - 
------------ Final Report Generation ------------
2025-05-19 16:41:36,382 - INFO - video_cleaner_2 - main - --------------------------------------------------
2025-05-19 16:41:36,382 - ERROR - video_cleaner_2 - main - Processing FAILED after 0.06s.
