"""
Unit tests for data classes.
"""

import unittest
from datetime import datetime
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from video_cleaner.core.data_classes import (
    WordDetection, ProcessingStats, ProcessingResults, 
    ProgressInfo, ValidationResult, FileInfo, ProcessingContext,
    ProgressTracker
)


class TestWordDetection(unittest.TestCase):
    """Test cases for WordDetection class."""
    
    def test_basic_creation(self):
        """Test basic WordDetection creation."""
        detection = WordDetection(
            word="test",
            start=1.5,
            end=2.5
        )
        
        self.assertEqual(detection.word, "test")
        self.assertEqual(detection.start, 1.5)
        self.assertEqual(detection.end, 2.5)
        self.assertIsNone(detection.subtitle_text)
        self.assertFalse(detection.verified)
        self.assertEqual(detection.reason, "")
        self.assertEqual(detection.confidence, 1.0)
    
    def test_full_creation(self):
        """Test WordDetection with all fields."""
        detection = WordDetection(
            word="badword",
            start=10.0,
            end=11.0,
            subtitle_text="This is a badword",
            verified=True,
            reason="Found in subtitle",
            confidence=0.95
        )
        
        self.assertEqual(detection.word, "badword")
        self.assertEqual(detection.start, 10.0)
        self.assertEqual(detection.end, 11.0)
        self.assertEqual(detection.subtitle_text, "This is a badword")
        self.assertTrue(detection.verified)
        self.assertEqual(detection.reason, "Found in subtitle")
        self.assertEqual(detection.confidence, 0.95)


class TestProcessingStats(unittest.TestCase):
    """Test cases for ProcessingStats class."""
    
    def test_default_stats(self):
        """Test default ProcessingStats values."""
        stats = ProcessingStats()
        
        self.assertEqual(stats.whisper_potential, 0)
        self.assertEqual(stats.verified_for_mute, 0)
        self.assertEqual(stats.skipped_no_subtitle, 0)
        self.assertEqual(stats.skipped_text_mismatch, 0)
        self.assertEqual(stats.ai_only_muted, 0)
        self.assertEqual(stats.total_processing_time, 0.0)
        self.assertEqual(stats.audio_duration, 0.0)
        self.assertEqual(stats.video_duration, 0.0)
    
    def test_custom_stats(self):
        """Test ProcessingStats with custom values."""
        stats = ProcessingStats(
            whisper_potential=5,
            verified_for_mute=3,
            total_processing_time=120.5
        )
        
        self.assertEqual(stats.whisper_potential, 5)
        self.assertEqual(stats.verified_for_mute, 3)
        self.assertEqual(stats.total_processing_time, 120.5)
        # Defaults should still be present
        self.assertEqual(stats.skipped_no_subtitle, 0)
    
    def test_to_dict(self):
        """Test ProcessingStats to_dict conversion."""
        stats = ProcessingStats(
            whisper_potential=2,
            verified_for_mute=1,
            ai_only_muted=1
        )
        
        result_dict = stats.to_dict()
        
        expected_keys = {
            "whisper_potential", "verified_for_mute", "skipped_no_subtitle",
            "skipped_text_mismatch", "ai_only_muted"
        }
        self.assertEqual(set(result_dict.keys()), expected_keys)
        self.assertEqual(result_dict["whisper_potential"], 2)
        self.assertEqual(result_dict["verified_for_mute"], 1)
        self.assertEqual(result_dict["ai_only_muted"], 1)


class TestProcessingResults(unittest.TestCase):
    """Test cases for ProcessingResults class."""
    
    def test_default_results(self):
        """Test default ProcessingResults values."""
        results = ProcessingResults()
        
        self.assertEqual(len(results.whisper_detections), 0)
        self.assertEqual(len(results.verification_details), 0)
        self.assertEqual(len(results.mute_segments), 0)
        self.assertIsInstance(results.stats, ProcessingStats)
        self.assertFalse(results.success)
        self.assertIsNone(results.error_message)
        self.assertEqual(results.processing_time, 0.0)
        self.assertIsInstance(results.timestamp, datetime)
    
    def test_results_with_data(self):
        """Test ProcessingResults with actual data."""
        detection1 = WordDetection(word="bad", start=1.0, end=2.0)
        detection2 = WordDetection(word="worse", start=5.0, end=6.0)
        
        stats = ProcessingStats(whisper_potential=2, verified_for_mute=1)
        
        results = ProcessingResults(
            whisper_detections=[detection1, detection2],
            verification_details=[detection1],
            mute_segments=[(1.0, 2.0)],
            stats=stats,
            success=True,
            processing_time=45.2
        )
        
        self.assertEqual(len(results.whisper_detections), 2)
        self.assertEqual(len(results.verification_details), 1)
        self.assertEqual(len(results.mute_segments), 1)
        self.assertEqual(results.mute_segments[0], (1.0, 2.0))
        self.assertTrue(results.success)
        self.assertEqual(results.processing_time, 45.2)
        self.assertEqual(results.stats.whisper_potential, 2)
    
    def test_to_dict_conversion(self):
        """Test ProcessingResults to_dict conversion for backward compatibility."""
        detection = WordDetection(
            word="test",
            start=1.0,
            end=2.0,
            verified=True,
            reason="test"
        )
        
        results = ProcessingResults(
            whisper_detections=[detection],
            verification_details=[detection],
            mute_segments=[(1.0, 2.0)],
            stats=ProcessingStats(whisper_potential=1)
        )
        
        result_dict = results.to_dict()
        
        # Check structure
        self.assertIn("whisper_detections", result_dict)
        self.assertIn("verification_details", result_dict)
        self.assertIn("mute_segments", result_dict)
        self.assertIn("stats", result_dict)
        
        # Check whisper_detections
        self.assertEqual(len(result_dict["whisper_detections"]), 1)
        detection_dict = result_dict["whisper_detections"][0]
        self.assertEqual(detection_dict["word"], "test")
        self.assertEqual(detection_dict["start"], 1.0)
        self.assertEqual(detection_dict["end"], 2.0)
        self.assertTrue(detection_dict["verified"])
        self.assertEqual(detection_dict["reason"], "test")
        
        # Check mute_segments
        self.assertEqual(result_dict["mute_segments"], [(1.0, 2.0)])
        
        # Check stats
        self.assertIsInstance(result_dict["stats"], dict)
        self.assertEqual(result_dict["stats"]["whisper_potential"], 1)


class TestValidationResult(unittest.TestCase):
    """Test cases for ValidationResult class."""
    
    def test_valid_result(self):
        """Test valid ValidationResult."""
        result = ValidationResult(is_valid=True)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.error_message, "")
        self.assertEqual(len(result.warnings), 0)
    
    def test_invalid_result(self):
        """Test invalid ValidationResult."""
        result = ValidationResult(is_valid=False, error_message="Test error")
        
        self.assertFalse(result.is_valid)
        self.assertEqual(result.error_message, "Test error")
    
    def test_add_warning(self):
        """Test adding warnings."""
        result = ValidationResult(is_valid=True)
        result.add_warning("Warning 1")
        result.add_warning("Warning 2")
        
        self.assertEqual(len(result.warnings), 2)
        self.assertIn("Warning 1", result.warnings)
        self.assertIn("Warning 2", result.warnings)
    
    def test_add_error(self):
        """Test adding error."""
        result = ValidationResult(is_valid=True)
        result.add_error("Test error")
        
        self.assertFalse(result.is_valid)
        self.assertEqual(result.error_message, "Test error")


class TestFileInfo(unittest.TestCase):
    """Test cases for FileInfo class."""
    
    def test_file_info_creation(self):
        """Test FileInfo creation."""
        file_info = FileInfo(
            path="/test/file.mp4",
            exists=True,
            size_bytes=1048576,  # 1MB
            duration_seconds=120.5
        )
        
        self.assertEqual(file_info.path, "/test/file.mp4")
        self.assertTrue(file_info.exists)
        self.assertEqual(file_info.size_bytes, 1048576)
        self.assertEqual(file_info.duration_seconds, 120.5)
    
    def test_size_mb_property(self):
        """Test size_mb property calculation."""
        file_info = FileInfo(
            path="test.mp4",
            exists=True,
            size_bytes=2097152  # 2MB
        )
        
        self.assertEqual(file_info.size_mb, 2.0)
    
    def test_duration_formatted_property(self):
        """Test duration_formatted property."""
        # Test seconds only
        file_info = FileInfo(path="test.mp4", exists=True, duration_seconds=45.5)
        self.assertEqual(file_info.duration_formatted, "45.5s")
        
        # Test minutes and seconds
        file_info = FileInfo(path="test.mp4", exists=True, duration_seconds=125.3)
        self.assertEqual(file_info.duration_formatted, "2m 5.3s")
        
        # Test hours, minutes, and seconds
        file_info = FileInfo(path="test.mp4", exists=True, duration_seconds=3725.7)
        self.assertEqual(file_info.duration_formatted, "1h 2m 5.7s")


class TestProgressTracker(unittest.TestCase):
    """Test cases for ProgressTracker class."""
    
    def test_progress_tracker_creation(self):
        """Test ProgressTracker creation."""
        tracker = ProgressTracker(total_steps=5)
        
        self.assertEqual(tracker.total_steps, 5)
        self.assertEqual(tracker.current_step, 0)
        self.assertIsNone(tracker.callback)
    
    def test_progress_update(self):
        """Test progress updates."""
        tracker = ProgressTracker(total_steps=4)
        
        # First update
        progress_info = tracker.update("Step 1")
        self.assertEqual(progress_info.step, 1)
        self.assertEqual(progress_info.total_steps, 4)
        self.assertEqual(progress_info.current_operation, "Step 1")
        self.assertEqual(progress_info.percentage, 25.0)
        
        # Second update
        progress_info = tracker.update("Step 2")
        self.assertEqual(progress_info.step, 2)
        self.assertEqual(progress_info.percentage, 50.0)
    
    def test_progress_complete(self):
        """Test progress completion."""
        tracker = ProgressTracker(total_steps=3)
        
        # Update to step 2
        tracker.update("Step 1")
        tracker.update("Step 2")
        
        # Complete
        progress_info = tracker.complete("Done")
        self.assertEqual(progress_info.step, 3)
        self.assertEqual(progress_info.percentage, 100.0)
        self.assertEqual(progress_info.current_operation, "Done")
    
    def test_progress_with_callback(self):
        """Test progress tracker with callback."""
        callback_calls = []
        
        def test_callback(percentage, message):
            callback_calls.append((percentage, message))
        
        tracker = ProgressTracker(total_steps=2, callback=test_callback)
        
        tracker.update("Test step")
        
        self.assertEqual(len(callback_calls), 1)
        self.assertEqual(callback_calls[0], (50.0, "Test step"))


if __name__ == '__main__':
    unittest.main()
