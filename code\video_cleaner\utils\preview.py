"""
Preview utilities for mute segments and video processing.
"""

import os
import tempfile
import logging
from typing import List, Tuple, Optional, Callable
import moviepy.editor as mp

from ..utils.helpers import video_clip_manager
from ..utils.cleanup import register_temp_file, TempFileContext
from ..core.config import get_config

logger = logging.getLogger(__name__)


def generate_preview_clip(video_path: str, mute_segment: Tuple[float, float], 
                         context_seconds: float = 2.0, 
                         output_path: Optional[str] = None) -> Optional[str]:
    """
    Generate a preview clip around a mute segment for review.
    
    Args:
        video_path: Path to the original video
        mute_segment: Tuple of (start, end) times for the mute segment
        context_seconds: Seconds of context to include before and after
        output_path: Optional output path (if None, creates temp file)
        
    Returns:
        Path to the preview clip or None if failed
    """
    try:
        start_time, end_time = mute_segment
        
        # Calculate preview bounds with context
        preview_start = max(0, start_time - context_seconds)
        preview_end = end_time + context_seconds
        
        logger.info(f"Generating preview clip: {preview_start:.2f}s - {preview_end:.2f}s")
        
        with video_clip_manager(video_path) as clip:
            # Ensure preview end doesn't exceed video duration
            preview_end = min(preview_end, clip.duration)
            
            # Create preview clip
            preview_clip = clip.subclip(preview_start, preview_end)
            
            # Generate output path if not provided
            if output_path is None:
                with TempFileContext(suffix=".mp4", prefix="preview_") as temp_path:
                    output_path = temp_path
                    register_temp_file(output_path)
            
            # Write preview clip with fast settings
            preview_clip.write_videofile(
                output_path,
                codec='libx264',
                preset='ultrafast',
                audio_codec='aac',
                logger=None,  # Suppress output
                temp_audiofile_path=None
            )
            
            preview_clip.close()
            
        logger.info(f"Preview clip generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Failed to generate preview clip: {e}")
        return None


def generate_mute_preview(video_path: str, mute_segment: Tuple[float, float],
                         context_seconds: float = 2.0,
                         output_path: Optional[str] = None) -> Optional[str]:
    """
    Generate a preview clip with the mute segment actually muted.
    
    Args:
        video_path: Path to the original video
        mute_segment: Tuple of (start, end) times for the mute segment
        context_seconds: Seconds of context to include before and after
        output_path: Optional output path (if None, creates temp file)
        
    Returns:
        Path to the muted preview clip or None if failed
    """
    try:
        start_time, end_time = mute_segment
        config = get_config()
        
        # Calculate preview bounds with context
        preview_start = max(0, start_time - context_seconds)
        preview_end = end_time + context_seconds
        
        logger.info(f"Generating muted preview clip: {preview_start:.2f}s - {preview_end:.2f}s")
        
        with video_clip_manager(video_path) as clip:
            # Ensure preview end doesn't exceed video duration
            preview_end = min(preview_end, clip.duration)
            
            # Create preview clip
            preview_clip = clip.subclip(preview_start, preview_end)
            
            # Apply muting to the segment within the preview
            # Adjust mute times relative to preview start
            relative_mute_start = max(0, start_time - preview_start)
            relative_mute_end = min(preview_end - preview_start, end_time - preview_start)
            
            # Create muted audio
            def mute_audio_segment(get_frame, t):
                frame = get_frame(t)
                # Mute if within the mute segment
                if relative_mute_start <= t <= relative_mute_end:
                    return frame * 0  # Silence
                return frame
            
            if preview_clip.audio:
                muted_audio = preview_clip.audio.fl(mute_audio_segment, keep_duration=True)
                preview_clip = preview_clip.set_audio(muted_audio)
            
            # Generate output path if not provided
            if output_path is None:
                with TempFileContext(suffix=".mp4", prefix="muted_preview_") as temp_path:
                    output_path = temp_path
                    register_temp_file(output_path)
            
            # Write preview clip
            preview_clip.write_videofile(
                output_path,
                codec='libx264',
                preset='ultrafast',
                audio_codec='aac',
                logger=None,
                temp_audiofile_path=None
            )
            
            preview_clip.close()
            
        logger.info(f"Muted preview clip generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Failed to generate muted preview clip: {e}")
        return None


def generate_comparison_preview(video_path: str, mute_segments: List[Tuple[float, float]],
                               context_seconds: float = 1.0,
                               max_segments: int = 5,
                               output_path: Optional[str] = None) -> Optional[str]:
    """
    Generate a comparison preview showing multiple mute segments side by side.
    
    Args:
        video_path: Path to the original video
        mute_segments: List of mute segments to preview
        context_seconds: Seconds of context around each segment
        max_segments: Maximum number of segments to include
        output_path: Optional output path (if None, creates temp file)
        
    Returns:
        Path to the comparison preview or None if failed
    """
    try:
        if not mute_segments:
            logger.warning("No mute segments provided for comparison preview")
            return None
        
        # Limit number of segments
        segments_to_process = mute_segments[:max_segments]
        
        logger.info(f"Generating comparison preview for {len(segments_to_process)} segments")
        
        preview_clips = []
        
        with video_clip_manager(video_path) as clip:
            for i, (start_time, end_time) in enumerate(segments_to_process):
                # Calculate preview bounds
                preview_start = max(0, start_time - context_seconds)
                preview_end = min(clip.duration, end_time + context_seconds)
                
                # Create segment clip
                segment_clip = clip.subclip(preview_start, preview_end)
                
                # Add text overlay showing segment number and timing
                text_overlay = (f"Segment {i+1}\n{start_time:.1f}s - {end_time:.1f}s")
                
                # Add text to clip (if moviepy supports it)
                try:
                    from moviepy.video.tools.drawing import color_gradient
                    from moviepy.video.fx.resize import resize
                    
                    # Resize to consistent size for comparison
                    segment_clip = resize(segment_clip, height=240)
                    
                except ImportError:
                    # Fallback if text features not available
                    pass
                
                preview_clips.append(segment_clip)
            
            # Arrange clips in a grid or sequence
            if len(preview_clips) == 1:
                final_clip = preview_clips[0]
            elif len(preview_clips) <= 2:
                # Side by side
                try:
                    from moviepy.video.compositing.concatenate import clips_array
                    final_clip = clips_array([preview_clips])
                except ImportError:
                    # Fallback to concatenation
                    from moviepy.video.compositing.concatenate import concatenate_videoclips
                    final_clip = concatenate_videoclips(preview_clips)
            else:
                # Concatenate all clips
                from moviepy.video.compositing.concatenate import concatenate_videoclips
                final_clip = concatenate_videoclips(preview_clips)
            
            # Generate output path if not provided
            if output_path is None:
                with TempFileContext(suffix=".mp4", prefix="comparison_preview_") as temp_path:
                    output_path = temp_path
                    register_temp_file(output_path)
            
            # Write final clip
            final_clip.write_videofile(
                output_path,
                codec='libx264',
                preset='fast',
                audio_codec='aac',
                logger=None
            )
            
            final_clip.close()
            for clip in preview_clips:
                clip.close()
        
        logger.info(f"Comparison preview generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Failed to generate comparison preview: {e}")
        return None


def extract_audio_segment(video_path: str, start_time: float, end_time: float,
                         output_path: Optional[str] = None) -> Optional[str]:
    """
    Extract audio segment for review.
    
    Args:
        video_path: Path to the original video
        start_time: Start time in seconds
        end_time: End time in seconds
        output_path: Optional output path (if None, creates temp file)
        
    Returns:
        Path to the extracted audio or None if failed
    """
    try:
        logger.info(f"Extracting audio segment: {start_time:.2f}s - {end_time:.2f}s")
        
        with video_clip_manager(video_path) as clip:
            if not clip.audio:
                logger.error("Video has no audio track")
                return None
            
            # Extract audio segment
            audio_segment = clip.audio.subclip(start_time, end_time)
            
            # Generate output path if not provided
            if output_path is None:
                with TempFileContext(suffix=".wav", prefix="audio_segment_") as temp_path:
                    output_path = temp_path
                    register_temp_file(output_path)
            
            # Write audio segment
            audio_segment.write_audiofile(
                output_path,
                codec='pcm_s16le',
                logger=None
            )
            
            audio_segment.close()
        
        logger.info(f"Audio segment extracted: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Failed to extract audio segment: {e}")
        return None


def create_preview_report(video_path: str, mute_segments: List[Tuple[float, float]],
                         output_dir: str) -> Optional[str]:
    """
    Create a comprehensive preview report with multiple preview clips.
    
    Args:
        video_path: Path to the original video
        mute_segments: List of mute segments
        output_dir: Directory to save preview files
        
    Returns:
        Path to the report HTML file or None if failed
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        report_path = os.path.join(output_dir, "preview_report.html")
        preview_files = []
        
        # Generate preview clips for first few segments
        max_previews = min(10, len(mute_segments))
        
        for i, segment in enumerate(mute_segments[:max_previews]):
            preview_path = os.path.join(output_dir, f"preview_{i+1}.mp4")
            
            if generate_preview_clip(video_path, segment, output_path=preview_path):
                preview_files.append({
                    'index': i + 1,
                    'segment': segment,
                    'file': os.path.basename(preview_path)
                })
        
        # Generate HTML report
        html_content = _generate_preview_html(video_path, mute_segments, preview_files)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Preview report created: {report_path}")
        return report_path
        
    except Exception as e:
        logger.error(f"Failed to create preview report: {e}")
        return None


def _generate_preview_html(video_path: str, mute_segments: List[Tuple[float, float]],
                          preview_files: List[dict]) -> str:
    """Generate HTML content for preview report."""
    from datetime import datetime
    
    html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Video Cleaner Preview Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .segment {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .preview-video {{ max-width: 400px; margin: 10px 0; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Video Cleaner Preview Report</h1>
        <p><strong>Video:</strong> {os.path.basename(video_path)}</p>
        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Total Mute Segments:</strong> {len(mute_segments)}</p>
        <p><strong>Total Muted Duration:</strong> {sum(end - start for start, end in mute_segments):.2f} seconds</p>
    </div>
    
    <h2>📊 Mute Segments Summary</h2>
    <table>
        <tr>
            <th>Segment</th>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Duration</th>
            <th>Preview</th>
        </tr>
"""
    
    for i, (start, end) in enumerate(mute_segments, 1):
        duration = end - start
        preview_available = any(p['index'] == i for p in preview_files)
        preview_cell = "✅ Available" if preview_available else "❌ Not generated"
        
        html += f"""
        <tr>
            <td>{i}</td>
            <td>{start:.3f}s</td>
            <td>{end:.3f}s</td>
            <td>{duration:.3f}s</td>
            <td>{preview_cell}</td>
        </tr>
"""
    
    html += """
    </table>
    
    <h2>🎥 Preview Clips</h2>
"""
    
    for preview in preview_files:
        start, end = preview['segment']
        duration = end - start
        
        html += f"""
    <div class="segment">
        <h3>Segment {preview['index']}: {start:.2f}s - {end:.2f}s ({duration:.2f}s)</h3>
        <video class="preview-video" controls>
            <source src="{preview['file']}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
"""
    
    html += """
    <div style="margin-top: 40px; padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
        <h3>📝 Instructions</h3>
        <ul>
            <li>Review each preview clip to verify the mute segments are correct</li>
            <li>Check that the muted content is appropriate for removal</li>
            <li>Ensure no important content is accidentally muted</li>
            <li>Use this report to make adjustments before final processing</li>
        </ul>
    </div>
</body>
</html>
"""
    
    return html
