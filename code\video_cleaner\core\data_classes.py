"""
Data classes for structured return types and results.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime


@dataclass
class WordDetection:
    """Represents a detected word with timing information."""
    word: str
    start: float
    end: float
    subtitle_text: Optional[str] = None
    verified: bool = False
    reason: str = ""
    confidence: float = 1.0  # For future use with confidence scoring


@dataclass
class ProcessingStats:
    """Statistics about the processing operation."""
    whisper_potential: int = 0
    verified_for_mute: int = 0
    skipped_no_subtitle: int = 0
    skipped_text_mismatch: int = 0
    ai_only_muted: int = 0
    total_processing_time: float = 0.0
    audio_duration: float = 0.0
    video_duration: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for backward compatibility."""
        return {
            "whisper_potential": self.whisper_potential,
            "verified_for_mute": self.verified_for_mute,
            "skipped_no_subtitle": self.skipped_no_subtitle,
            "skipped_text_mismatch": self.skipped_text_mismatch,
            "ai_only_muted": self.ai_only_muted
        }


@dataclass
class ProcessingResults:
    """Complete results from video processing operation."""
    whisper_detections: List[WordDetection] = field(default_factory=list)
    verification_details: List[WordDetection] = field(default_factory=list)
    mute_segments: List[Tuple[float, float]] = field(default_factory=list)
    stats: ProcessingStats = field(default_factory=ProcessingStats)
    success: bool = False
    error_message: Optional[str] = None
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for backward compatibility."""
        return {
            "whisper_detections": [
                {
                    "word": d.word,
                    "start": d.start,
                    "end": d.end,
                    "subtitle_text": d.subtitle_text,
                    "verified": d.verified,
                    "reason": d.reason
                }
                for d in self.whisper_detections
            ],
            "verification_details": [
                {
                    "word": d.word,
                    "start": d.start,
                    "end": d.end,
                    "subtitle_text": d.subtitle_text,
                    "verified": d.verified,
                    "reason": d.reason
                }
                for d in self.verification_details
            ],
            "mute_segments": self.mute_segments,
            "stats": self.stats.to_dict()
        }


@dataclass
class ProgressInfo:
    """Information about processing progress."""
    step: int
    total_steps: int
    current_operation: str
    percentage: float = 0.0
    estimated_time_remaining: Optional[float] = None
    
    @property
    def progress_text(self) -> str:
        """Get formatted progress text."""
        return f"Step {self.step}/{self.total_steps}: {self.current_operation} ({self.percentage:.1f}%)"


@dataclass
class ValidationResult:
    """Result of input validation."""
    is_valid: bool
    error_message: str = ""
    warnings: List[str] = field(default_factory=list)
    
    def add_warning(self, warning: str):
        """Add a warning message."""
        self.warnings.append(warning)
    
    def add_error(self, error: str):
        """Add an error and mark as invalid."""
        self.is_valid = False
        self.error_message = error


@dataclass
class FileInfo:
    """Information about a file."""
    path: str
    exists: bool
    size_bytes: int = 0
    duration_seconds: float = 0.0
    format_info: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def size_mb(self) -> float:
        """Get file size in megabytes."""
        return self.size_bytes / (1024 * 1024)
    
    @property
    def duration_formatted(self) -> str:
        """Get formatted duration string."""
        if self.duration_seconds < 60:
            return f"{self.duration_seconds:.1f}s"
        elif self.duration_seconds < 3600:
            minutes = int(self.duration_seconds // 60)
            seconds = self.duration_seconds % 60
            return f"{minutes}m {seconds:.1f}s"
        else:
            hours = int(self.duration_seconds // 3600)
            minutes = int((self.duration_seconds % 3600) // 60)
            seconds = self.duration_seconds % 60
            return f"{hours}h {minutes}m {seconds:.1f}s"


@dataclass
class ProcessingContext:
    """Context information for processing operations."""
    video_file: FileInfo
    subtitle_file: Optional[FileInfo] = None
    words_file: Optional[FileInfo] = None
    output_path: str = ""
    model_size: str = "medium"
    processing_mode: str = "ai_only"  # "ai_only" or "subtitle_verification"
    
    def validate(self) -> ValidationResult:
        """Validate the processing context."""
        result = ValidationResult(is_valid=True)
        
        if not self.video_file.exists:
            result.add_error(f"Video file does not exist: {self.video_file.path}")
        
        if self.subtitle_file and not self.subtitle_file.exists:
            result.add_warning(f"Subtitle file does not exist: {self.subtitle_file.path}")
            self.processing_mode = "ai_only"
        elif self.subtitle_file and self.subtitle_file.exists:
            self.processing_mode = "subtitle_verification"
        
        if self.words_file and not self.words_file.exists:
            result.add_error(f"Words file does not exist: {self.words_file.path}")
        
        if not self.output_path:
            result.add_error("Output path is required")
        
        return result


class ProgressTracker:
    """Track and report progress for long-running operations."""
    
    def __init__(self, total_steps: int, callback: Optional[callable] = None):
        self.total_steps = total_steps
        self.current_step = 0
        self.callback = callback
        self.start_time = datetime.now()
        
    def update(self, message: str, step_increment: int = 1) -> ProgressInfo:
        """Update progress and return progress info."""
        self.current_step += step_increment
        percentage = (self.current_step / self.total_steps) * 100
        
        # Calculate estimated time remaining
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if self.current_step > 0:
            time_per_step = elapsed / self.current_step
            remaining_steps = self.total_steps - self.current_step
            estimated_remaining = time_per_step * remaining_steps
        else:
            estimated_remaining = None
        
        progress_info = ProgressInfo(
            step=self.current_step,
            total_steps=self.total_steps,
            current_operation=message,
            percentage=percentage,
            estimated_time_remaining=estimated_remaining
        )
        
        if self.callback:
            self.callback(percentage, message)
        
        return progress_info
    
    def complete(self, message: str = "Processing completed") -> ProgressInfo:
        """Mark processing as complete."""
        self.current_step = self.total_steps
        return self.update(message, 0)
