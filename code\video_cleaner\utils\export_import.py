"""
Export and import utilities for word lists and processing results.
"""

import csv
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from ..core.data_classes import ProcessingResults, WordDetection

logger = logging.getLogger(__name__)


def export_detected_words_csv(results: ProcessingResults, output_path: str) -> bool:
    """
    Export detected words to CSV file for review and analysis.
    
    Args:
        results: Processing results containing detected words
        output_path: Path to save the CSV file
        
    Returns:
        True if export successful, False otherwise
    """
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'word', 'start_time', 'end_time', 'duration', 'verified', 
                'reason', 'subtitle_text', 'confidence'
            ])
            writer.writeheader()
            
            for detection in results.whisper_detections:
                duration = detection.end - detection.start
                writer.writerow({
                    'word': detection.word,
                    'start_time': f"{detection.start:.3f}",
                    'end_time': f"{detection.end:.3f}",
                    'duration': f"{duration:.3f}",
                    'verified': detection.verified,
                    'reason': detection.reason,
                    'subtitle_text': detection.subtitle_text or '',
                    'confidence': detection.confidence
                })
        
        logger.info(f"Exported {len(results.whisper_detections)} detected words to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export detected words to CSV: {e}")
        return False


def export_processing_results_json(results: ProcessingResults, output_path: str) -> bool:
    """
    Export complete processing results to JSON file.
    
    Args:
        results: Processing results to export
        output_path: Path to save the JSON file
        
    Returns:
        True if export successful, False otherwise
    """
    try:
        # Convert to dictionary with additional metadata
        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'export_version': '2.0',
            'processing_results': results.to_dict(),
            'summary': {
                'total_detections': len(results.whisper_detections),
                'verified_detections': len([d for d in results.verification_details if d.verified]),
                'mute_segments_count': len(results.mute_segments),
                'total_muted_duration': sum(end - start for start, end in results.mute_segments),
                'processing_time': results.processing_time,
                'success': results.success
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Exported processing results to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export processing results to JSON: {e}")
        return False


def export_mute_segments_csv(results: ProcessingResults, output_path: str) -> bool:
    """
    Export mute segments to CSV file for external editing tools.
    
    Args:
        results: Processing results containing mute segments
        output_path: Path to save the CSV file
        
    Returns:
        True if export successful, False otherwise
    """
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'segment_number', 'start_time', 'end_time', 'duration', 
                'start_timecode', 'end_timecode'
            ])
            writer.writeheader()
            
            for i, (start, end) in enumerate(results.mute_segments, 1):
                duration = end - start
                start_timecode = _seconds_to_timecode(start)
                end_timecode = _seconds_to_timecode(end)
                
                writer.writerow({
                    'segment_number': i,
                    'start_time': f"{start:.3f}",
                    'end_time': f"{end:.3f}",
                    'duration': f"{duration:.3f}",
                    'start_timecode': start_timecode,
                    'end_timecode': end_timecode
                })
        
        logger.info(f"Exported {len(results.mute_segments)} mute segments to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export mute segments to CSV: {e}")
        return False


def import_word_list_txt(file_path: str) -> Optional[List[str]]:
    """
    Import word list from text file.
    
    Args:
        file_path: Path to the text file
        
    Returns:
        List of words or None if import failed
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            words = [line.strip().lower() for line in f if line.strip()]
        
        # Remove duplicates while preserving order
        unique_words = []
        seen = set()
        for word in words:
            if word not in seen:
                unique_words.append(word)
                seen.add(word)
        
        logger.info(f"Imported {len(unique_words)} unique words from {file_path}")
        return unique_words
        
    except Exception as e:
        logger.error(f"Failed to import word list from {file_path}: {e}")
        return None


def import_word_list_csv(file_path: str, word_column: str = 'word') -> Optional[List[str]]:
    """
    Import word list from CSV file.
    
    Args:
        file_path: Path to the CSV file
        word_column: Name of the column containing words
        
    Returns:
        List of words or None if import failed
    """
    try:
        words = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            if word_column not in reader.fieldnames:
                logger.error(f"Column '{word_column}' not found in CSV. Available columns: {reader.fieldnames}")
                return None
            
            for row in reader:
                word = row[word_column].strip().lower()
                if word:
                    words.append(word)
        
        # Remove duplicates while preserving order
        unique_words = []
        seen = set()
        for word in words:
            if word not in seen:
                unique_words.append(word)
                seen.add(word)
        
        logger.info(f"Imported {len(unique_words)} unique words from {file_path}")
        return unique_words
        
    except Exception as e:
        logger.error(f"Failed to import word list from CSV {file_path}: {e}")
        return None


def export_word_list_txt(words: List[str], output_path: str) -> bool:
    """
    Export word list to text file.
    
    Args:
        words: List of words to export
        output_path: Path to save the text file
        
    Returns:
        True if export successful, False otherwise
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for word in words:
                f.write(f"{word}\n")
        
        logger.info(f"Exported {len(words)} words to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export word list to {output_path}: {e}")
        return False


def export_word_list_csv(words: List[str], output_path: str, 
                        include_metadata: bool = True) -> bool:
    """
    Export word list to CSV file with optional metadata.
    
    Args:
        words: List of words to export
        output_path: Path to save the CSV file
        include_metadata: Whether to include metadata columns
        
    Returns:
        True if export successful, False otherwise
    """
    try:
        fieldnames = ['word']
        if include_metadata:
            fieldnames.extend(['length', 'category', 'severity', 'notes'])
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for word in words:
                row = {'word': word}
                if include_metadata:
                    row.update({
                        'length': len(word),
                        'category': _categorize_word(word),
                        'severity': 'medium',  # Default severity
                        'notes': ''
                    })
                writer.writerow(row)
        
        logger.info(f"Exported {len(words)} words to CSV {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to export word list to CSV {output_path}: {e}")
        return False


def merge_word_lists(*word_lists: List[str]) -> List[str]:
    """
    Merge multiple word lists, removing duplicates.
    
    Args:
        *word_lists: Variable number of word lists to merge
        
    Returns:
        Merged list with duplicates removed
    """
    merged = []
    seen = set()
    
    for word_list in word_lists:
        for word in word_list:
            word_lower = word.lower().strip()
            if word_lower and word_lower not in seen:
                merged.append(word_lower)
                seen.add(word_lower)
    
    logger.info(f"Merged {len(word_lists)} word lists into {len(merged)} unique words")
    return merged


def validate_word_list(words: List[str]) -> Dict[str, Any]:
    """
    Validate a word list and return statistics.
    
    Args:
        words: List of words to validate
        
    Returns:
        Dictionary with validation results and statistics
    """
    stats = {
        'total_words': len(words),
        'unique_words': len(set(word.lower() for word in words)),
        'empty_entries': sum(1 for word in words if not word.strip()),
        'very_long_words': sum(1 for word in words if len(word) > 50),
        'very_short_words': sum(1 for word in words if len(word) < 2),
        'numeric_entries': sum(1 for word in words if word.isdigit()),
        'average_length': sum(len(word) for word in words) / len(words) if words else 0,
        'categories': {}
    }
    
    # Categorize words
    for word in words:
        category = _categorize_word(word)
        stats['categories'][category] = stats['categories'].get(category, 0) + 1
    
    # Validation warnings
    warnings = []
    if stats['empty_entries'] > 0:
        warnings.append(f"{stats['empty_entries']} empty entries found")
    if stats['very_long_words'] > 0:
        warnings.append(f"{stats['very_long_words']} very long words (>50 chars)")
    if stats['numeric_entries'] > 0:
        warnings.append(f"{stats['numeric_entries']} numeric entries found")
    if stats['total_words'] != stats['unique_words']:
        duplicates = stats['total_words'] - stats['unique_words']
        warnings.append(f"{duplicates} duplicate words found")
    
    stats['warnings'] = warnings
    stats['is_valid'] = len(warnings) == 0
    
    return stats


def _seconds_to_timecode(seconds: float) -> str:
    """Convert seconds to HH:MM:SS.mmm timecode format."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"


def _categorize_word(word: str) -> str:
    """Categorize a word based on its characteristics."""
    word_lower = word.lower()
    
    if len(word) < 2:
        return "very_short"
    elif len(word) > 20:
        return "very_long"
    elif word.isdigit():
        return "numeric"
    elif any(char.isdigit() for char in word):
        return "alphanumeric"
    elif any(char in "!@#$%^&*()_+-=[]{}|;:,.<>?" for char in word):
        return "special_chars"
    else:
        return "standard"


def create_word_list_report(words: List[str], output_path: str) -> bool:
    """
    Create a detailed report about a word list.
    
    Args:
        words: List of words to analyze
        output_path: Path to save the report
        
    Returns:
        True if report created successfully, False otherwise
    """
    try:
        stats = validate_word_list(words)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("WORD LIST ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("STATISTICS\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total words: {stats['total_words']}\n")
            f.write(f"Unique words: {stats['unique_words']}\n")
            f.write(f"Average length: {stats['average_length']:.1f} characters\n")
            f.write(f"Empty entries: {stats['empty_entries']}\n")
            f.write(f"Very long words (>50 chars): {stats['very_long_words']}\n")
            f.write(f"Very short words (<2 chars): {stats['very_short_words']}\n")
            f.write(f"Numeric entries: {stats['numeric_entries']}\n\n")
            
            f.write("CATEGORIES\n")
            f.write("-" * 20 + "\n")
            for category, count in stats['categories'].items():
                percentage = (count / stats['total_words']) * 100
                f.write(f"{category}: {count} ({percentage:.1f}%)\n")
            f.write("\n")
            
            if stats['warnings']:
                f.write("WARNINGS\n")
                f.write("-" * 20 + "\n")
                for warning in stats['warnings']:
                    f.write(f"⚠️  {warning}\n")
                f.write("\n")
            
            f.write("WORD LIST\n")
            f.write("-" * 20 + "\n")
            for i, word in enumerate(sorted(set(word.lower() for word in words)), 1):
                f.write(f"{i:4d}. {word}\n")
        
        logger.info(f"Created word list report: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create word list report: {e}")
        return False
