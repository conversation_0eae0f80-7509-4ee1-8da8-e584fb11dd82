"""
Unit tests for validation utilities.
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from video_cleaner.utils.validation import (
    validate_video_file, validate_subtitle_file, validate_words_file,
    validate_output_path, validate_all_inputs
)


class TestVideoFileValidation(unittest.TestCase):
    """Test cases for video file validation."""
    
    def test_empty_path(self):
        """Test validation with empty path."""
        result = validate_video_file("")
        
        self.assertFalse(result.is_valid)
        self.assertIn("empty", result.error_message.lower())
    
    def test_nonexistent_file(self):
        """Test validation with non-existent file."""
        result = validate_video_file("nonexistent_video.mp4")
        
        self.assertFalse(result.is_valid)
        self.assertIn("does not exist", result.error_message)
    
    def test_directory_instead_of_file(self):
        """Test validation when path points to directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = validate_video_file(temp_dir)
            
            self.assertFalse(result.is_valid)
            self.assertIn("not a file", result.error_message)
    
    def test_empty_file(self):
        """Test validation with empty file."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_file = f.name
        
        try:
            result = validate_video_file(temp_file)
            
            self.assertFalse(result.is_valid)
            self.assertIn("empty", result.error_message.lower())
            
        finally:
            os.unlink(temp_file)
    
    def test_file_extension_warning(self):
        """Test warning for unusual file extension."""
        with tempfile.NamedTemporaryFile(suffix=".xyz", delete=False) as f:
            f.write(b"dummy content")
            temp_file = f.name
        
        try:
            result = validate_video_file(temp_file)
            
            # Should be invalid due to other reasons, but should have extension warning
            self.assertTrue(any("extension" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)


class TestSubtitleFileValidation(unittest.TestCase):
    """Test cases for subtitle file validation."""
    
    def test_empty_path(self):
        """Test validation with empty path (should be valid since optional)."""
        result = validate_subtitle_file("")
        
        self.assertTrue(result.is_valid)
    
    def test_nonexistent_file(self):
        """Test validation with non-existent file."""
        result = validate_subtitle_file("nonexistent.srt")
        
        self.assertFalse(result.is_valid)
        self.assertIn("does not exist", result.error_message)
    
    def test_empty_subtitle_file(self):
        """Test validation with empty subtitle file."""
        with tempfile.NamedTemporaryFile(suffix=".srt", delete=False) as f:
            temp_file = f.name
        
        try:
            result = validate_subtitle_file(temp_file)
            
            self.assertFalse(result.is_valid)
            self.assertIn("empty", result.error_message.lower())
            
        finally:
            os.unlink(temp_file)
    
    def test_valid_srt_file(self):
        """Test validation with valid SRT content."""
        srt_content = """1
00:00:01,000 --> 00:00:03,000
Hello world

2
00:00:04,000 --> 00:00:06,000
Test subtitle
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".srt", delete=False) as f:
            f.write(srt_content)
            temp_file = f.name
        
        try:
            result = validate_subtitle_file(temp_file)
            
            self.assertTrue(result.is_valid)
            
        finally:
            os.unlink(temp_file)
    
    def test_invalid_srt_format(self):
        """Test validation with invalid SRT format."""
        invalid_content = "This is not a valid SRT file"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".srt", delete=False) as f:
            f.write(invalid_content)
            temp_file = f.name
        
        try:
            result = validate_subtitle_file(temp_file)
            
            # Should have warning about format
            self.assertTrue(any("srt format" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)
    
    def test_unusual_extension(self):
        """Test warning for unusual subtitle file extension."""
        with tempfile.NamedTemporaryFile(mode='w', suffix=".xyz", delete=False) as f:
            f.write("1\n00:00:01,000 --> 00:00:03,000\nTest")
            temp_file = f.name
        
        try:
            result = validate_subtitle_file(temp_file)
            
            # Should have warning about extension
            self.assertTrue(any("extension" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)


class TestWordsFileValidation(unittest.TestCase):
    """Test cases for words file validation."""
    
    def test_empty_path(self):
        """Test validation with empty path."""
        result = validate_words_file("")
        
        self.assertFalse(result.is_valid)
        self.assertIn("empty", result.error_message.lower())
    
    def test_nonexistent_file(self):
        """Test validation with non-existent file."""
        result = validate_words_file("nonexistent.txt")
        
        self.assertFalse(result.is_valid)
        self.assertIn("does not exist", result.error_message)
    
    def test_empty_words_file(self):
        """Test validation with empty words file."""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as f:
            temp_file = f.name
        
        try:
            result = validate_words_file(temp_file)
            
            # Should be valid but with warning
            self.assertTrue(result.is_valid)
            self.assertTrue(any("empty" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)
    
    def test_valid_words_file(self):
        """Test validation with valid words file."""
        words_content = "word1\nword2\nword3\n"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as f:
            f.write(words_content)
            temp_file = f.name
        
        try:
            result = validate_words_file(temp_file)
            
            self.assertTrue(result.is_valid)
            self.assertEqual(len(result.warnings), 0)
            
        finally:
            os.unlink(temp_file)
    
    def test_words_file_with_empty_lines(self):
        """Test validation with words file containing empty lines."""
        words_content = "word1\n\nword2\n   \nword3\n"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as f:
            f.write(words_content)
            temp_file = f.name
        
        try:
            result = validate_words_file(temp_file)
            
            self.assertTrue(result.is_valid)
            # Should handle empty lines gracefully
            
        finally:
            os.unlink(temp_file)
    
    def test_very_large_words_file(self):
        """Test validation with very large words file."""
        # Create file with many words
        words = [f"word{i}" for i in range(15000)]
        words_content = "\n".join(words)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as f:
            f.write(words_content)
            temp_file = f.name
        
        try:
            result = validate_words_file(temp_file)
            
            self.assertTrue(result.is_valid)
            # Should have warning about large word list
            self.assertTrue(any("large word list" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)
    
    def test_words_with_very_long_entries(self):
        """Test validation with very long word entries."""
        long_word = "a" * 100  # 100 character "word"
        words_content = f"word1\n{long_word}\nword2\n"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as f:
            f.write(words_content)
            temp_file = f.name
        
        try:
            result = validate_words_file(temp_file)
            
            self.assertTrue(result.is_valid)
            # Should have warning about long words
            self.assertTrue(any("long words" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)


class TestOutputPathValidation(unittest.TestCase):
    """Test cases for output path validation."""
    
    def test_empty_output_path(self):
        """Test validation with empty output path."""
        result = validate_output_path("")
        
        self.assertFalse(result.is_valid)
        self.assertIn("empty", result.error_message.lower())
    
    def test_valid_output_path(self):
        """Test validation with valid output path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "output.mp4")
            result = validate_output_path(output_path)
            
            self.assertTrue(result.is_valid)
    
    def test_output_path_in_nonexistent_directory(self):
        """Test validation when output directory doesn't exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create path in subdirectory that doesn't exist
            output_path = os.path.join(temp_dir, "subdir", "output.mp4")
            result = validate_output_path(output_path)
            
            # Should be valid (directory will be created)
            self.assertTrue(result.is_valid)
            # Directory should have been created
            self.assertTrue(os.path.exists(os.path.dirname(output_path)))
    
    def test_existing_output_file_warning(self):
        """Test warning when output file already exists."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_file = f.name
        
        try:
            result = validate_output_path(temp_file)
            
            self.assertTrue(result.is_valid)
            # Should have warning about overwriting
            self.assertTrue(any("overwritten" in warning.lower() for warning in result.warnings))
            
        finally:
            os.unlink(temp_file)
    
    def test_same_as_input_path(self):
        """Test error when output path is same as input path."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_file = f.name
        
        try:
            result = validate_output_path(temp_file, input_path=temp_file)
            
            self.assertFalse(result.is_valid)
            self.assertIn("same as input", result.error_message.lower())
            
        finally:
            os.unlink(temp_file)
    
    def test_different_extension_warning(self):
        """Test warning when output extension differs from input."""
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input.mp4")
            output_path = os.path.join(temp_dir, "output.avi")
            
            result = validate_output_path(output_path, input_path=input_path)
            
            self.assertTrue(result.is_valid)
            # Should have warning about different extension
            self.assertTrue(any("differs from input" in warning.lower() for warning in result.warnings))


class TestAllInputsValidation(unittest.TestCase):
    """Test cases for validate_all_inputs function."""
    
    def test_all_invalid_inputs(self):
        """Test validation when all inputs are invalid."""
        result = validate_all_inputs(
            video_path="",
            srt_path="nonexistent.srt",
            words_path="",
            output_path=""
        )
        
        self.assertFalse(result.is_valid)
        # Should have error message from first failing validation
        self.assertIn("empty", result.error_message.lower())
    
    def test_mixed_valid_invalid_inputs(self):
        """Test validation with mix of valid and invalid inputs."""
        # Create valid words file
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as f:
            f.write("word1\nword2\n")
            words_file = f.name
        
        try:
            result = validate_all_inputs(
                video_path="nonexistent.mp4",  # Invalid
                srt_path="",  # Valid (optional)
                words_path=words_file,  # Valid
                output_path="/tmp/output.mp4"  # Valid
            )
            
            self.assertFalse(result.is_valid)
            # Should fail on video file
            self.assertIn("does not exist", result.error_message)
            
        finally:
            os.unlink(words_file)
    
    def test_all_valid_inputs_minimal(self):
        """Test validation with minimal valid inputs."""
        # Create temporary files
        with tempfile.NamedTemporaryFile(delete=False) as video_file:
            video_file.write(b"dummy video content")
            video_path = video_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False) as words_file:
            words_file.write("word1\nword2\n")
            words_path = words_file.name
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "output.mp4")
            
            try:
                result = validate_all_inputs(
                    video_path=video_path,
                    srt_path="",  # Optional
                    words_path=words_path,
                    output_path=output_path
                )
                
                # Note: This will likely fail due to video file validation
                # (not a real video), but the structure should work
                # The important thing is that it processes all validations
                
            finally:
                os.unlink(video_path)
                os.unlink(words_path)


if __name__ == '__main__':
    unittest.main()
