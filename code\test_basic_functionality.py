#!/usr/bin/env python3
"""
Basic functionality test for the video cleaner application.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the video_cleaner package to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from video_cleaner.core.config import ProcessingConfig, get_config
        print("✅ Config module imported successfully")
        
        from video_cleaner.core.data_classes import ProcessingResults, WordDetection
        print("✅ Data classes module imported successfully")
        
        from video_cleaner.utils.validation import validate_video_file
        print("✅ Validation module imported successfully")
        
        from video_cleaner.utils.cleanup import register_temp_file
        print("✅ Cleanup module imported successfully")
        
        from video_cleaner.utils.performance import get_memory_info
        print("✅ Performance module imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_configuration():
    """Test configuration management."""
    print("\nTesting configuration...")
    
    try:
        from video_cleaner.core.config import ProcessingConfig, get_config, set_config
        
        # Test default config
        config = get_config()
        print(f"✅ Default config loaded: model_size={config.model_size}")
        
        # Test custom config
        custom_config = ProcessingConfig(model_size="large", mute_padding_start=0.1)
        set_config(custom_config)
        
        updated_config = get_config()
        assert updated_config.model_size == "large"
        assert updated_config.mute_padding_start == 0.1
        print("✅ Custom configuration works")
        
        # Test save/load
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            success = custom_config.save_to_file(config_file)
            assert success, "Failed to save config"
            
            loaded_config = ProcessingConfig.load_from_file(config_file)
            assert loaded_config.model_size == "large"
            print("✅ Config save/load works")
            
        finally:
            os.unlink(config_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_data_classes():
    """Test data classes functionality."""
    print("\nTesting data classes...")
    
    try:
        from video_cleaner.core.data_classes import WordDetection, ProcessingResults, ProcessingStats
        
        # Test WordDetection
        detection = WordDetection(
            word="test",
            start=1.0,
            end=2.0,
            verified=True,
            reason="test detection"
        )
        assert detection.word == "test"
        print("✅ WordDetection works")
        
        # Test ProcessingResults
        results = ProcessingResults()
        results.whisper_detections.append(detection)
        results.stats.whisper_potential = 1
        
        # Test backward compatibility
        results_dict = results.to_dict()
        assert "whisper_detections" in results_dict
        assert "stats" in results_dict
        print("✅ ProcessingResults and backward compatibility work")
        
        return True
        
    except Exception as e:
        print(f"❌ Data classes test failed: {e}")
        return False


def test_validation():
    """Test validation functionality."""
    print("\nTesting validation...")
    
    try:
        from video_cleaner.utils.validation import validate_video_file, validate_words_file
        
        # Test with non-existent file
        result = validate_video_file("nonexistent.mp4")
        assert not result.is_valid
        assert "does not exist" in result.error_message
        print("✅ Video file validation works")
        
        # Test words file validation with temp file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("word1\nword2\nword3\n")
            words_file = f.name
        
        try:
            result = validate_words_file(words_file)
            assert result.is_valid
            print("✅ Words file validation works")
            
        finally:
            os.unlink(words_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        return False


def test_performance_monitoring():
    """Test performance monitoring."""
    print("\nTesting performance monitoring...")
    
    try:
        from video_cleaner.utils.performance import get_memory_info, PerformanceProfiler
        
        # Test memory info
        memory_info = get_memory_info()
        assert memory_info.total_mb > 0
        assert memory_info.percent_used >= 0
        print(f"✅ Memory monitoring works: {memory_info}")
        
        # Test performance profiler
        profiler = PerformanceProfiler()
        profiler.start("test_operation")
        
        import time
        time.sleep(0.1)  # Simulate work
        
        duration = profiler.end("test_operation")
        assert duration >= 0.1
        print(f"✅ Performance profiler works: {duration:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False


def test_cleanup():
    """Test cleanup functionality."""
    print("\nTesting cleanup...")
    
    try:
        from video_cleaner.utils.cleanup import TempFileContext, register_temp_file, safe_remove_file
        
        # Test temp file context
        with TempFileContext(suffix=".test") as temp_file:
            assert os.path.exists(temp_file)
            with open(temp_file, 'w') as f:
                f.write("test")
        
        # File should be cleaned up automatically
        assert not os.path.exists(temp_file)
        print("✅ Temp file context works")
        
        # Test safe file removal
        with tempfile.NamedTemporaryFile(delete=False) as f:
            test_file = f.name
        
        success = safe_remove_file(test_file)
        assert success
        assert not os.path.exists(test_file)
        print("✅ Safe file removal works")
        
        return True
        
    except Exception as e:
        print(f"❌ Cleanup test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Running Video Cleaner Basic Functionality Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_data_classes,
        test_validation,
        test_performance_monitoring,
        test_cleanup
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The modular structure is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
