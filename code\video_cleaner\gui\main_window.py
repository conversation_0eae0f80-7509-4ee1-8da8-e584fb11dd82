"""
Main GUI window for the video cleaner application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext, font
import threading
import subprocess
import urllib.request
import urllib.error
import os
import sys
import time
from queue import Queue, Empty
from typing import Optional
import logging

from ..core.config import ProcessingConfig, get_config, set_config
from ..core.transcription import load_foul_words, transcribe_audio, find_mute_segments_verified
from ..core.audio_processor import extract_audio
from ..core.video_processor import create_muted_video
from ..core.subtitle_parser import parse_subtitles
from ..utils.logging import setup_logging, get_logger, QueueHandler
from ..utils.performance import get_memory_info
from ..utils.cleanup import register_cleanup_callback, cleanup_now
from .batch_processing import BatchProcessingDialog


class VideoCleanerGUI:
    """Main GUI application for video cleaning."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 Video Cleaner - AI-Powered Foul Word Muter v2.0")
        self.root.geometry("1000x750")
        self.root.minsize(900, 650)

        # Configure modern styling
        self.setup_styles()

        # Initialize variables
        self.video_path = tk.StringVar()
        self.srt_path = tk.StringVar()

        # Set default foul words file if it exists
        default_words_file = "foul_words.txt"
        if os.path.exists(default_words_file):
            self.words_file = tk.StringVar(value=default_words_file)
        else:
            self.words_file = tk.StringVar()

        self.model_size = tk.StringVar(value=get_config().model_size)
        self.output_path = tk.StringVar()
        self.log_level = tk.StringVar(value="INFO")

        # Transcription backend (Whisper or Voxtral) and Voxtral settings
        cfg = get_config()
        self.transcription_backend = tk.StringVar(value=getattr(cfg, 'transcription_backend', 'whisper'))
        self.voxtral_api_base = tk.StringVar(value=getattr(cfg, 'voxtral_api_base', '') or '')
        self.voxtral_model = tk.StringVar(value=getattr(cfg, 'voxtral_model', 'mistralai/Voxtral-Mini-3B-2507'))
        self.voxtral_api_key = tk.StringVar(value=getattr(cfg, 'voxtral_api_key', '') or '')
        self.voxtral_timeout = tk.IntVar(value=getattr(cfg, 'voxtral_timeout', 600))

        # Processing state
        self.processing = False
        self.processing_thread = None
        self.log_queue = Queue()

        # Setup GUI
        self.setup_gui()
        self.setup_logging()

        # Start log monitoring
        self.monitor_log_queue()

        # Register cleanup callbacks
        register_cleanup_callback(self.cleanup_gui_resources)

    def setup_styles(self):
        """Configure modern styling for the GUI."""
        style = ttk.Style()
        
        # Configure modern theme
        if "vista" in style.theme_names():
            style.theme_use("vista")
        elif "clam" in style.theme_names():
            style.theme_use("clam")
        
        # Custom styles
        style.configure('Modern.TFrame', background='#f0f0f0')
        style.configure('Header.TLabel', font=('Segoe UI', 16, 'bold'), background='#f0f0f0')
        style.configure('Subheader.TLabel', font=('Segoe UI', 12, 'bold'), background='#f0f0f0')
        style.configure('Modern.TButton', font=('Segoe UI', 10))
        style.configure('Success.TButton', font=('Segoe UI', 10, 'bold'))
        style.configure('Danger.TButton', font=('Segoe UI', 10, 'bold'))

    def setup_gui(self):
        """Create and layout the GUI components."""
        # Menu bar
        self.setup_menubar()
        # Create header
        self.create_header()

        # Create main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Main tab
        main_frame = ttk.Frame(notebook, style='Modern.TFrame')
        notebook.add(main_frame, text="🎯 Main")

        # Log tab
        log_frame = ttk.Frame(notebook, style='Modern.TFrame')
        notebook.add(log_frame, text="📋 Logs")

        # Settings tab
        settings_frame = ttk.Frame(notebook, style='Modern.TFrame')
        notebook.add(settings_frame, text="⚙️ Settings")

        self.setup_main_tab(main_frame)
        self.setup_log_tab(log_frame)
        self.setup_settings_tab(settings_frame)

    def create_header(self):
        """Create the application header."""
        header_frame = ttk.Frame(self.root, style='Modern.TFrame')
        header_frame.pack(fill=tk.X, padx=15, pady=15)

        # Title
        title_label = ttk.Label(
            header_frame, 
            text="🎬 Video Cleaner", 
            style='Header.TLabel'
        )
        title_label.pack(side=tk.LEFT)

        # Version info
        version_label = ttk.Label(
            header_frame, 
            text="v2.0 - AI-Powered Foul Word Muter", 
            font=('Segoe UI', 10, 'italic'),
            foreground='#666666'
        )
        version_label.pack(side=tk.LEFT, padx=(10, 0))

        # Status indicator
        self.status_frame = ttk.Frame(header_frame)
        self.status_frame.pack(side=tk.RIGHT)

        self.status_label = ttk.Label(
            self.status_frame, 
            text="Ready", 
            font=('Segoe UI', 10, 'bold'),
            foreground='#008000'
        )
        self.status_label.pack(side=tk.RIGHT)

    def setup_main_tab(self, parent):
        """Setup the main processing tab."""
        # Create scrollable frame
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # File selection section
        file_frame = ttk.LabelFrame(scrollable_frame, text="📁 File Selection", padding=20)
        file_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Video file
        self.create_file_input(file_frame, "🎬 Video File:", self.video_path,
                              self.browse_video_file, 0,
                              "Select your video file (MP4, AVI, MOV, etc.)")

        # Subtitle file
        self.create_file_input(file_frame, "📝 Subtitle File (.srt) - Optional:", self.srt_path,
                              self.browse_srt_file, 1,
                              "Optional: Select subtitle file for verification (improves accuracy). Leave empty for AI-only detection.")

        # Foul words file
        self.create_file_input(file_frame, "🚫 Foul Words File:", self.words_file,
                              self.browse_words_file, 2,
                              "Text file containing words to mute")

        # Output file
        self.create_file_input(file_frame, "💾 Output File:", self.output_path,
                              self.browse_output_file, 3,
                              "Where to save the processed video")

        # Configuration section
        config_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Configuration", padding=20)
        config_frame.pack(fill=tk.X, padx=15, pady=10)

        # Model selection
        ttk.Label(config_frame, text="🤖 AI Model:", font=('Segoe UI', 12, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))

        model_frame = ttk.Frame(config_frame)
        model_frame.grid(row=1, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 15))

        models = ["tiny", "base", "small", "medium", "large"]
        for i, model in enumerate(models):
            ttk.Radiobutton(
                model_frame, 
                text=f"{model.title()} ({'Fast' if model in ['tiny', 'base'] else 'Balanced' if model == 'medium' else 'Accurate'})",
                variable=self.model_size, 
                value=model
            ).grid(row=0, column=i, padx=(0, 15), sticky=tk.W)

        # Transcription backend
        ttk.Label(config_frame, text="🛠️ Transcription Backend:", font=('Segoe UI', 12, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))

        backend_frame = ttk.Frame(config_frame)
        backend_frame.grid(row=3, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 10))

        ttk.Radiobutton(
            backend_frame,
            text="Whisper (stable-ts)",
            variable=self.transcription_backend,
            value="whisper",
            command=self.on_backend_change,
        ).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

        ttk.Radiobutton(
            backend_frame,
            text="Voxtral (local server)",
            variable=self.transcription_backend,
            value="voxtral",
            command=self.on_backend_change,
        ).grid(row=0, column=1, padx=(0, 15), sticky=tk.W)

        # Voxtral settings
        self.voxtral_frame = ttk.Frame(config_frame)
        self.voxtral_frame.grid(row=4, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 10))

        ttk.Label(self.voxtral_frame, text="API Base:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(self.voxtral_frame, textvariable=self.voxtral_api_base, width=36).grid(row=0, column=1, padx=(5, 15), sticky=tk.W)

        ttk.Label(self.voxtral_frame, text="Model:").grid(row=0, column=2, sticky=tk.W)
        ttk.Entry(self.voxtral_frame, textvariable=self.voxtral_model, width=32).grid(row=0, column=3, padx=(5, 15), sticky=tk.W)

        ttk.Label(self.voxtral_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, pady=(6, 0))
        ttk.Entry(self.voxtral_frame, textvariable=self.voxtral_api_key, width=36, show="*").grid(row=1, column=1, padx=(5, 15), pady=(6, 0), sticky=tk.W)

        ttk.Label(self.voxtral_frame, text="Timeout (s):").grid(row=1, column=2, sticky=tk.W, pady=(6, 0))
        ttk.Spinbox(self.voxtral_frame, from_=60, to=7200, increment=30, textvariable=self.voxtral_timeout, width=10).grid(row=1, column=3, padx=(5, 0), pady=(6, 0), sticky=tk.W)

        # Initialize visibility/state based on current selection
        self.on_backend_change()

        # Log level selection
        ttk.Label(config_frame, text="📊 Log Level:", font=('Segoe UI', 12, 'bold')).grid(
            row=5, column=0, sticky=tk.W, pady=(15, 5))

        log_combo = ttk.Combobox(
            config_frame, 
            textvariable=self.log_level,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly",
            width=15
        )
        log_combo.grid(row=6, column=0, sticky=tk.W, pady=(0, 15))

        # Processing section
        process_frame = ttk.LabelFrame(scrollable_frame, text="🚀 Processing", padding=20)
        process_frame.pack(fill=tk.X, padx=15, pady=10)

        # Progress bar and status text
        self.progress_bar = ttk.Progressbar(process_frame, mode='determinate', maximum=100, value=0)
        self.progress_bar.pack(fill=tk.X, pady=(0, 8))
        self.progress_text_label = ttk.Label(process_frame, text="Idle", foreground="#333333")
        self.progress_text_label.pack(fill=tk.X, pady=(0, 7))

        # Control buttons
        button_frame = ttk.Frame(process_frame)
        button_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(
            button_frame, 
            text="▶️ Start Processing", 
            command=self.start_processing,
            style='Success.TButton'
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(
            button_frame, 
            text="⏹️ Stop", 
            command=self.stop_processing,
            style='Danger.TButton',
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            button_frame,
            text="📦 Batch Processing",
            command=self.open_batch_processing
        ).pack(side=tk.RIGHT, padx=(0, 10))

        ttk.Button(
            button_frame,
            text="📂 Open Output Folder",
            command=self.open_output_folder
        ).pack(side=tk.RIGHT)

        # Footer status bar
        footer = ttk.Frame(self.root)
        footer.pack(fill=tk.X, side=tk.BOTTOM)
        self.footer_label = ttk.Label(footer, text="Ready", anchor="w")
        self.footer_label.pack(fill=tk.X, padx=10, pady=6)
        self.update_status_metrics()

    def setup_menubar(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)

        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open Video...", command=self.browse_video_file, accelerator="Ctrl+O")
        file_menu.add_command(label="Open Subtitles...", command=self.browse_srt_file)
        file_menu.add_command(label="Open Words File...", command=self.browse_words_file)
        file_menu.add_separator()
        file_menu.add_command(label="Save Config...", command=self.save_configuration)
        file_menu.add_command(label="Load Config...", command=self.load_configuration)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit, accelerator="Alt+F4")
        menubar.add_cascade(label="File", menu=file_menu)

        tools_menu = tk.Menu(menubar, tearoff=0)
        tools_menu.add_command(label="Batch Processing", command=self.open_batch_processing)
        tools_menu.add_command(label="Open Output Folder", command=self.open_output_folder)
        menubar.add_cascade(label="Tools", menu=tools_menu)

        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_command(label="Clear Logs", command=self.clear_logs)
        menubar.add_cascade(label="View", menu=view_menu)

        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)

        self.root.config(menu=menubar)
        # Keyboard shortcuts
        self.root.bind_all("<Control-o>", lambda e: self.browse_video_file())

    def create_file_input(self, parent, label_text, var, browse_command, row, tooltip=""):
        """Create a file input row with label, entry, and browse button."""
        ttk.Label(parent, text=label_text, font=('Segoe UI', 11, 'bold')).grid(
            row=row*2, column=0, sticky=tk.W, pady=(10 if row > 0 else 0, 5))

        if tooltip:
            ttk.Label(parent, text=tooltip, font=('Segoe UI', 9), foreground='#666666').grid(
                row=row*2, column=1, sticky=tk.W, padx=(10, 0), pady=(10 if row > 0 else 0, 5))

        entry_frame = ttk.Frame(parent)
        entry_frame.grid(row=row*2+1, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 10))
        entry_frame.columnconfigure(0, weight=1)

        entry = ttk.Entry(entry_frame, textvariable=var, font=('Segoe UI', 10))
        entry.grid(row=0, column=0, sticky=tk.W+tk.E, padx=(0, 10))

        ttk.Button(entry_frame, text="Browse...", command=browse_command).grid(row=0, column=1)

    def setup_log_tab(self, parent):
        """Setup the log viewing tab."""
        log_frame = ttk.Frame(parent, style='Modern.TFrame')
        log_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Log controls
        control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(control_frame, text="Clear Logs", command=self.clear_logs).pack(side=tk.LEFT)
        ttk.Button(control_frame, text="Save Logs", command=self.save_logs).pack(side=tk.LEFT, padx=(10, 0))

        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            wrap=tk.WORD,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def setup_settings_tab(self, parent):
        """Setup the settings/configuration tab."""
        settings_frame = ttk.Frame(parent, style='Modern.TFrame')
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Configuration section
        config_section = ttk.LabelFrame(settings_frame, text="⚙️ Processing Configuration", padding=20)
        config_section.pack(fill=tk.X, pady=(0, 15))

        config = get_config()

        # Mute padding settings
        ttk.Label(config_section, text="Mute Padding (seconds):", font=('Segoe UI', 11, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 10))

        padding_frame = ttk.Frame(config_section)
        padding_frame.grid(row=1, column=0, sticky=tk.W, pady=(0, 15))

        ttk.Label(padding_frame, text="Start:").grid(row=0, column=0, sticky=tk.W)
        self.padding_start_var = tk.DoubleVar(value=config.mute_padding_start)
        ttk.Spinbox(padding_frame, from_=0.0, to=1.0, increment=0.01, width=10,
                   textvariable=self.padding_start_var).grid(row=0, column=1, padx=(5, 15))

        ttk.Label(padding_frame, text="End:").grid(row=0, column=2, sticky=tk.W)
        self.padding_end_var = tk.DoubleVar(value=config.mute_padding_end)
        ttk.Spinbox(padding_frame, from_=0.0, to=1.0, increment=0.01, width=10,
                   textvariable=self.padding_end_var).grid(row=0, column=3, padx=(5, 0))

        # Video quality settings
        ttk.Label(config_section, text="Video Quality:", font=('Segoe UI', 11, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=(15, 10))

        quality_frame = ttk.Frame(config_section)
        quality_frame.grid(row=3, column=0, sticky=tk.W, pady=(0, 6))

        # Simple quality preset selector (maps to CRF/Preset below)
        ttk.Label(quality_frame, text="Preset:").grid(row=0, column=0, sticky=tk.W)
        self.quality_preset_var = tk.StringVar(value="Preserve Original (default)")
        quality_presets = [
            "Preserve Original (default)",
            "High Quality (CRF 18, slow)",
            "Balanced (CRF 20, medium)",
            "Smaller File (CRF 23, fast)"
        ]
        preset_combo = ttk.Combobox(
            quality_frame,
            textvariable=self.quality_preset_var,
            values=quality_presets,
            width=28,
            state="readonly",
        )
        preset_combo.grid(row=0, column=1, padx=(5, 15))
        preset_combo.bind("<<ComboboxSelected>>", lambda e: self.on_quality_preset_change())

        # Advanced controls (still available)
        ttk.Label(quality_frame, text="CRF:").grid(row=0, column=2, sticky=tk.W)
        self.crf_var = tk.StringVar(value=config.video_crf)
        ttk.Combobox(
            quality_frame,
            textvariable=self.crf_var,
            values=["15", "18", "20", "23", "28"],
            width=8,
            state="readonly",
        ).grid(row=0, column=3, padx=(5, 15))

        ttk.Label(quality_frame, text="Encoder Preset:").grid(row=0, column=4, sticky=tk.W)
        self.preset_var = tk.StringVar(value=config.video_preset)
        ttk.Combobox(
            quality_frame,
            textvariable=self.preset_var,
            values=[
                "ultrafast",
                "superfast",
                "veryfast",
                "faster",
                "fast",
                "medium",
                "slow",
                "slower",
                "veryslow",
            ],
            width=12,
            state="readonly",
        ).grid(row=0, column=5, padx=(5, 0))

        # Clarifying note
        ttk.Label(
            config_section,
            text=(
                "Note: Final export preserves the original video stream (no re-encode) by default.\n"
                "These settings apply to preview clips and the rare fallback encoder path."
            ),
            foreground="#666666",
        ).grid(row=4, column=0, sticky=tk.W, pady=(6, 0))

        # Save/Load configuration
        config_buttons = ttk.Frame(config_section)
        config_buttons.grid(row=5, column=0, sticky=tk.W, pady=(15, 0))

        ttk.Button(config_buttons, text="Save Configuration", command=self.save_configuration).pack(side=tk.LEFT)
        ttk.Button(config_buttons, text="Load Configuration", command=self.load_configuration).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(config_buttons, text="Reset to Defaults", command=self.reset_configuration).pack(side=tk.LEFT, padx=(10, 0))

    def on_quality_preset_change(self):
        """Map the simple quality preset to CRF and encoder preset values."""
        choice = self.quality_preset_var.get()
        if choice.startswith("Preserve Original"):
            # Keep current CRF/preset; main export copies video stream anyway
            return
        if choice.startswith("High Quality"):
            self.crf_var.set("18")
            self.preset_var.set("slow")
            return
        if choice.startswith("Balanced"):
            self.crf_var.set("20")
            self.preset_var.set("medium")
            return
        if choice.startswith("Smaller File"):
            self.crf_var.set("23")
            self.preset_var.set("fast")
            return

    def on_backend_change(self):
        """Show/hide Voxtral settings depending on selected backend."""
        try:
            backend = self.transcription_backend.get()
        except Exception:
            backend = 'whisper'
        try:
            if backend == 'voxtral':
                self.voxtral_frame.grid()
                # Provide a sensible default API base if empty
                if not self.voxtral_api_base.get().strip():
                    self.voxtral_api_base.set("http://127.0.0.1:8000/v1")
                # Attempt to ensure server is running in background
                threading.Thread(target=self.ensure_voxtral_server_running, daemon=True).start()
            else:
                # Hide Voxtral settings when not needed
                self.voxtral_frame.grid_remove()
        except Exception:
            pass

    def _check_voxtral_health(self, api_base: str) -> bool:
        """Return True if a server responds at the given OpenAI-compatible base (checks /models)."""
        try:
            url = api_base.rstrip('/') + "/models"
            with urllib.request.urlopen(url, timeout=2) as resp:
                return 200 <= resp.getcode() < 300
        except Exception:
            return False

    def ensure_voxtral_server_running(self) -> bool:
        """Ensure a local Voxtral (vLLM OpenAI server) is up; try to start if not.
        Returns True if server is reachable; False otherwise.
        """
        api_base = (self.voxtral_api_base.get().strip() or "http://127.0.0.1:8000/v1")
        if self._check_voxtral_health(api_base):
            return True

        # Try to spawn a local server
        model = self.voxtral_model.get().strip() or "mistralai/Voxtral-Mini-3B-2507"
        host = "127.0.0.1"
        port = "8000"
        log_path = os.path.join(os.getcwd(), "voxtral_server.log")
        cmd = [
            sys.executable, "-m", "vllm.entrypoints.openai.api_server",
            "--model", model,
            "--host", host,
            "--port", port
        ]
        try:
            with open(log_path, "a", encoding="utf-8") as log_file:
                creationflags = 0
                if sys.platform == "win32":
                    creationflags = getattr(subprocess, 'DETACHED_PROCESS', 0) | getattr(subprocess, 'CREATE_NEW_PROCESS_GROUP', 0)
                subprocess.Popen(
                    cmd,
                    stdout=log_file,
                    stderr=log_file,
                    stdin=subprocess.DEVNULL,
                    creationflags=creationflags,
                )
        except FileNotFoundError:
            # Python executable not found (unlikely here)
            return False
        except Exception:
            # vllm may not be installed or Windows unsupported
            return False

        # Wait briefly for server to become healthy
        for _ in range(30):  # ~30 * 0.5s = 15s
            if self._check_voxtral_health(api_base):
                return True
            try:
                time.sleep(0.5)
            except Exception:
                break
        return False

    def setup_logging(self):
        """Setup logging to display in the GUI."""
        # Create queue handler for GUI display
        queue_handler = QueueHandler(self.log_queue)
        queue_handler.setLevel(logging.INFO)

        # Get the root logger and add our handler
        logger = get_logger()
        logger.addHandler(queue_handler)

    def monitor_log_queue(self):
        """Monitor the log queue and update the GUI with memory leak prevention."""
        config = get_config()
        processed_count = 0
        max_batch_size = 50  # Process at most 50 messages per call

        try:
            while processed_count < max_batch_size:
                record = self.log_queue.get_nowait()

                # Format the log message with timestamp
                timestamp = time.strftime("%H:%M:%S", time.localtime(record.created))
                formatted_message = f"[{timestamp}] [{record.levelname}] {record.getMessage()}\n"

                # Add to log text widget
                self.log_text.config(state=tk.NORMAL)
                self.log_text.insert(tk.END, formatted_message)

                # Color code by level with more efficient tagging
                line_start = f"end-{len(formatted_message.splitlines())}l"
                line_end = "end-1l"

                if record.levelname == "ERROR":
                    self.log_text.tag_add("error", line_start, line_end)
                    self.log_text.tag_config("error", foreground="red", font=('Consolas', 9, 'bold'))
                elif record.levelname == "WARNING":
                    self.log_text.tag_add("warning", line_start, line_end)
                    self.log_text.tag_config("warning", foreground="orange", font=('Consolas', 9, 'bold'))
                elif record.levelname == "INFO":
                    self.log_text.tag_add("info", line_start, line_end)
                    self.log_text.tag_config("info", foreground="blue")
                elif record.levelname == "DEBUG":
                    self.log_text.tag_add("debug", line_start, line_end)
                    self.log_text.tag_config("debug", foreground="gray")

                processed_count += 1

        except Empty:
            pass

        # Limit log display to prevent memory issues - do this after processing batch
        try:
            self.log_text.config(state=tk.NORMAL)
            line_count = int(self.log_text.index('end-1c').split('.')[0])
            if line_count > config.max_log_lines:
                # Remove old lines more aggressively to prevent memory buildup
                lines_to_remove = line_count - config.max_log_lines + 100  # Remove extra buffer
                self.log_text.delete('1.0', f'{lines_to_remove}.0')

                # Clear old tags to prevent memory leak
                for tag in ["error", "warning", "info", "debug"]:
                    try:
                        self.log_text.tag_delete(tag)
                    except tk.TclError:
                        pass  # Tag might not exist

            self.log_text.config(state=tk.DISABLED)
            self.log_text.see(tk.END)

        except Exception as e:
            # Fallback: clear all text if there's an issue
            try:
                self.log_text.config(state=tk.NORMAL)
                self.log_text.delete('1.0', tk.END)
                self.log_text.insert(tk.END, f"[LOG] Log display reset due to error: {e}\n")
                self.log_text.config(state=tk.DISABLED)
            except:
                pass  # Last resort: ignore errors

        # Schedule next check with adaptive timing
        if self.processing:
            # More frequent checks during processing
            next_check = 100 if self.log_queue.qsize() > 10 else 250
        else:
            # Less frequent checks when idle
            next_check = 500 if self.log_queue.qsize() > 0 else 2000

        self.root.after(next_check, self.monitor_log_queue)

    def update_status_metrics(self):
        """Update footer status bar with memory information periodically."""
        try:
            mem = get_memory_info()
            self.footer_label.config(text=f"Ready | Memory: {mem.used_mb:.0f}/{mem.total_mb:.0f} MB ({mem.percent_used:.0f}%)")
        except Exception:
            pass
        # Update every 2 seconds
        self.root.after(2000, self.update_status_metrics)

    # File browsing methods
    def browse_video_file(self):
        """Browse for video file."""
        filename = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                ("MP4 files", "*.mp4"),
                ("AVI files", "*.avi"),
                ("MOV files", "*.mov"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.video_path.set(filename)
            # Auto-generate output path
            if not self.output_path.get():
                base_name = os.path.splitext(filename)[0]
                extension = os.path.splitext(filename)[1]
                self.output_path.set(f"{base_name}{get_config().output_suffix}{extension}")

    def browse_srt_file(self):
        """Browse for SRT subtitle file."""
        filename = filedialog.askopenfilename(
            title="Select Subtitle File",
            filetypes=[
                ("SRT files", "*.srt"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.srt_path.set(filename)

    def browse_words_file(self):
        """Browse for foul words file."""
        filename = filedialog.askopenfilename(
            title="Select Foul Words File",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.words_file.set(filename)

    def browse_output_file(self):
        """Browse for output file location."""
        filename = filedialog.asksaveasfilename(
            title="Save Output Video As",
            filetypes=[
                ("MP4 files", "*.mp4"),
                ("AVI files", "*.avi"),
                ("MOV files", "*.mov"),
                ("All files", "*.*")
            ],
            defaultextension=".mp4"
        )
        if filename:
            self.output_path.set(filename)

    # Log management methods
    def clear_logs(self):
        """Clear the log display."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_logs(self):
        """Save logs to a file."""
        filename = filedialog.asksaveasfilename(
            title="Save Logs As",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")],
            defaultextension=".log"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("Success", f"Logs saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save logs: {e}")

    # Configuration methods
    def save_configuration(self):
        """Save current configuration to file."""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration As",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            try:
                config = get_config()
                config.mute_padding_start = self.padding_start_var.get()
                config.mute_padding_end = self.padding_end_var.get()
                config.video_crf = self.crf_var.get()
                config.video_preset = self.preset_var.get()
                config.model_size = self.model_size.get()
                # Transcription backend settings
                config.transcription_backend = self.transcription_backend.get()
                config.voxtral_api_base = self.voxtral_api_base.get().strip() or None
                config.voxtral_model = self.voxtral_model.get().strip()
                config.voxtral_api_key = self.voxtral_api_key.get().strip() or None
                try:
                    config.voxtral_timeout = int(self.voxtral_timeout.get())
                except Exception:
                    pass

                if config.save_to_file(filename):
                    messagebox.showinfo("Success", f"Configuration saved to {filename}")
                else:
                    messagebox.showerror("Error", "Failed to save configuration")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def load_configuration(self):
        """Load configuration from file."""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                config = ProcessingConfig.load_from_file(filename)
                set_config(config)

                # Update GUI variables
                self.padding_start_var.set(config.mute_padding_start)
                self.padding_end_var.set(config.mute_padding_end)
                self.crf_var.set(config.video_crf)
                self.preset_var.set(config.video_preset)
                self.model_size.set(config.model_size)
                # Backend
                self.transcription_backend.set(getattr(config, 'transcription_backend', 'whisper'))
                self.voxtral_api_base.set(getattr(config, 'voxtral_api_base', '') or '')
                self.voxtral_model.set(getattr(config, 'voxtral_model', 'mistralai/Voxtral-Mini-3B-2507'))
                self.voxtral_api_key.set(getattr(config, 'voxtral_api_key', '') or '')
                self.voxtral_timeout.set(getattr(config, 'voxtral_timeout', 600))
                self.on_backend_change()

                messagebox.showinfo("Success", f"Configuration loaded from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def reset_configuration(self):
        """Reset configuration to defaults."""
        if messagebox.askyesno("Confirm Reset", "Reset all settings to default values?"):
            config = ProcessingConfig()
            set_config(config)

            # Update GUI variables
            self.padding_start_var.set(config.mute_padding_start)
            self.padding_end_var.set(config.mute_padding_end)
            self.crf_var.set(config.video_crf)
            self.preset_var.set(config.video_preset)
            self.model_size.set(config.model_size)
            # Backend defaults
            self.transcription_backend.set(getattr(config, 'transcription_backend', 'whisper'))
            self.voxtral_api_base.set(getattr(config, 'voxtral_api_base', '') or '')
            self.voxtral_model.set(getattr(config, 'voxtral_model', 'mistralai/Voxtral-Mini-3B-2507'))
            self.voxtral_api_key.set(getattr(config, 'voxtral_api_key', '') or '')
            self.voxtral_timeout.set(getattr(config, 'voxtral_timeout', 600))
            self.on_backend_change()

            messagebox.showinfo("Success", "Configuration reset to defaults")

    def open_output_folder(self):
        """Open the output folder in file explorer."""
        output_path = self.output_path.get()
        if output_path and os.path.exists(output_path):
            folder = os.path.dirname(output_path)
            if sys.platform == "win32":
                os.startfile(folder)
            elif sys.platform == "darwin":
                os.system(f"open '{folder}'")
            else:
                os.system(f"xdg-open '{folder}'")
        else:
            messagebox.showwarning("Warning", "Output file does not exist yet.")

    def open_batch_processing(self):
        """Open the batch processing dialog."""
        try:
            BatchProcessingDialog(
                self.root,
                words_file=self.words_file.get(),
                model_size=self.model_size.get()
            )
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open batch processing: {e}")

    # Processing methods
    def validate_inputs(self) -> bool:
        """Validate user inputs before processing."""
        if not self.video_path.get():
            messagebox.showerror("Error", "Please select a video file.")
            return False

        if not os.path.exists(self.video_path.get()):
            messagebox.showerror("Error", "Video file does not exist.")
            return False

        if not self.words_file.get():
            messagebox.showerror("Error", "Please select a foul words file.")
            return False

        if not os.path.exists(self.words_file.get()):
            messagebox.showerror("Error", "Foul words file does not exist.")
            return False

        if self.srt_path.get() and not os.path.exists(self.srt_path.get()):
            messagebox.showerror("Error", "Subtitle file does not exist.")
            return False

        if not self.output_path.get():
            messagebox.showerror("Error", "Please specify an output file path.")
            return False

        # Check if output directory exists
        output_dir = os.path.dirname(self.output_path.get())
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                messagebox.showerror("Error", f"Cannot create output directory: {e}")
                return False

        # Prevent output from overwriting input
        try:
            if os.path.abspath(self.output_path.get()) == os.path.abspath(self.video_path.get()):
                messagebox.showerror("Error", "Output path cannot be the same as input video path.")
                return False
        except Exception:
            return False

        # If output file already exists, ask for confirmation to overwrite
        try:
            if os.path.exists(self.output_path.get()):
                if not messagebox.askyesno(
                    "Overwrite File",
                    f"Output file already exists:\n{self.output_path.get()}\n\nDo you want to overwrite it?",
                ):
                    return False
        except Exception:
            # Be conservative if dialog fails for any reason
            return False

        return True

    def start_processing(self):
        """Start the video processing in a separate thread."""
        if not self.validate_inputs():
            return

        if self.processing:
            messagebox.showwarning("Warning", "Processing is already in progress.")
            return

        # Update configuration from GUI
        config = get_config()
        config.mute_padding_start = self.padding_start_var.get()
        config.mute_padding_end = self.padding_end_var.get()
        config.video_crf = self.crf_var.get()
        config.video_preset = self.preset_var.get()
        config.model_size = self.model_size.get()

        # Update logging level
        log_level = getattr(logging, self.log_level.get().upper())
        logger = get_logger()
        logger.setLevel(log_level)

        # Clear logs
        self.clear_logs()

        # Update UI state
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.config(mode='determinate', maximum=100, value=0)
        self.status_label.config(text="Processing...", foreground='#ff8c00')
        self.progress_text_label.config(text="Starting...")

        # Start processing thread
        self.processing_thread = threading.Thread(target=self.process_video, daemon=True)
        self.processing_thread.start()

    def stop_processing(self):
        """Stop the current processing."""
        if self.processing:
            self.processing = False
            self.status_label.config(text="Stopping...", foreground='#ff0000')

            # Note: The actual stopping will be handled by the processing thread
            # when it checks the self.processing flag

    def process_video(self):
        """Process the video in a separate thread."""
        logger = get_logger()
        start_time = time.time()
        success = False

        try:
            # Create arguments object similar to CLI
            class Args:
                def __init__(self, video_path, srt_path, words_file, model, output_path):
                    self.video_path = os.path.abspath(video_path)
                    self.srt_path = os.path.abspath(srt_path) if srt_path else None
                    self.words_file = os.path.abspath(words_file)
                    self.model = model
                    self.output_path = os.path.abspath(output_path)

            args = Args(
                self.video_path.get(),
                self.srt_path.get(),
                self.words_file.get(),
                self.model_size.get(),
                self.output_path.get()
            )
            config = get_config()

            # Setup logging for this session
            base_filename = os.path.splitext(os.path.basename(args.video_path))[0]
            input_dir = os.path.dirname(args.video_path)
            log_file = os.path.join(input_dir, f"{base_filename}{config.log_suffix}")

            # Add file logging
            setup_logging(log_file, logger.level, self.log_queue)

            # Ensure runtime config reflects GUI selections BEFORE logging
            cfg = get_config()
            cfg.transcription_backend = self.transcription_backend.get()
            cfg.voxtral_api_base = self.voxtral_api_base.get().strip() or None
            cfg.voxtral_model = self.voxtral_model.get().strip()
            cfg.voxtral_api_key = self.voxtral_api_key.get().strip() or None
            try:
                cfg.voxtral_timeout = int(self.voxtral_timeout.get())
            except Exception:
                pass

            logger.info("Starting Video Cleaner GUI processing...")
            logger.info(f"Input video: {args.video_path}")
            logger.info(f"Output video: {args.output_path}")
            logger.info(f"Subtitle file: {args.srt_path or 'None (AI-only mode)'}")
            logger.info(f"Foul words file: {args.words_file}")
            logger.info(f"Transcription backend: {cfg.transcription_backend}")
            if cfg.transcription_backend == 'whisper':
                logger.info(f"Whisper model: {args.model}")
            else:
                logger.info(f"Voxtral model: {cfg.voxtral_model}")

            # 1. Load Foul Words
            logger.info("-" * 20 + " Step 1: Load Foul Words " + "-" * 20)
            foul_words = load_foul_words(args.words_file)
            if foul_words is None:
                raise RuntimeError("Failed to load foul words.")
            if not foul_words:
                logger.warning("Foul words list is empty. No words will be muted.")

            if not self.processing:
                return

            # 2. Parse Subtitles (if provided)
            logger.info("-" * 20 + " Step 2: Parse Subtitles " + "-" * 20)
            if args.srt_path:
                logger.info(f"Subtitle file provided: '{args.srt_path}' - will use subtitle verification mode.")
                subtitles = parse_subtitles(args.srt_path)
                if subtitles is None:
                    raise RuntimeError("Failed to parse subtitles.")
            else:
                logger.info("No subtitle file provided - will use AI-only detection mode.")
                subtitles = None

            if not self.processing:
                return

            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Extracting audio..."))

            # 3. Extract Audio
            logger.info("-" * 20 + " Step 3: Extract Audio " + "-" * 20)
            if not extract_audio(args.video_path, config.temp_audio_filename, progress_callback=self.ui_progress_callback):
                raise RuntimeError("Failed to extract audio from video.")

            if not self.processing:
                return

            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Transcribing audio (this may take a while)..."))

            # 4. Transcribe Audio
            logger.info("-" * 20 + " Step 4: Transcribe Audio " + "-" * 20)
            word_timestamps = transcribe_audio(config.temp_audio_filename, args.model, progress_callback=self.ui_progress_callback)
            if word_timestamps is None:
                raise RuntimeError("Audio transcription failed.")

            if not self.processing:
                return

            # Update status
            if subtitles:
                self.root.after(0, lambda: self.status_label.config(text="Verifying foul words..."))
            else:
                self.root.after(0, lambda: self.status_label.config(text="Processing foul words (AI-only)..."))

            # 5. Find Mute Segments (Verified or AI-only)
            if subtitles:
                logger.info("-" * 20 + " Step 5: Verify and Find Mute Segments " + "-" * 20)
            else:
                logger.info("-" * 20 + " Step 5: Find Mute Segments (AI-only) " + "-" * 20)
            results_data = find_mute_segments_verified(word_timestamps, foul_words or [], subtitles)
            mute_segments = results_data.mute_segments

            if not self.processing:
                return

            # 6. Create Muted Video
            logger.info("-" * 20 + " Step 6: Create Muted Video " + "-" * 20)
            if mute_segments:
                # Update status
                self.root.after(0, lambda: self.status_label.config(text="Creating muted video..."))

                if not create_muted_video(args.video_path, args.output_path, mute_segments, progress_callback=self.ui_progress_callback):
                    raise RuntimeError("Failed to create the final muted video.")
                else:
                    logger.info(f"Muted video successfully created at '{args.output_path}'")
                    success = True
            else:
                logger.info("No verified foul words found requiring muting. Output video was not created/modified.")
                success = True

            # 7. Generate Report
            logger.info("-" * 20 + " Step 7: Generate Report " + "-" * 20)
            processing_time = time.time() - start_time
            self.generate_report(results_data, args, processing_time)

            logger.info(f"Processing completed successfully in {processing_time:.2f} seconds!")

        except Exception as e:
            logger.exception(f"Processing failed: {e}")
            processing_time = time.time() - start_time

            # Update UI on main thread
            error_message = f"Processing failed after {processing_time:.2f} seconds.\nError: {str(e)}\nCheck the logs for details."
            self.root.after(0, lambda: self.processing_finished(False, error_message))
            return

        finally:
            # Clean up temporary audio file
            config = get_config()
            if os.path.exists(config.temp_audio_filename):
                try:
                    os.remove(config.temp_audio_filename)
                    logger.debug(f"Cleaned up temporary audio file: {config.temp_audio_filename}")
                except Exception as e:
                    logger.warning(f"Could not remove temporary audio file: {e}")

        # Update UI on main thread
        if success:
            if mute_segments:
                mode_text = "subtitle verification" if args.srt_path else "AI-only detection"
                message = f"Processing completed successfully!\n\nMuted video saved to:\n{args.output_path}\n\nMode: {mode_text}\nProcessing time: {processing_time:.2f} seconds"
            else:
                mode_text = "subtitle verification" if args.srt_path else "AI-only detection"
                message = f"Processing completed successfully!\n\nNo foul words required muting.\n\nMode: {mode_text}\nProcessing time: {processing_time:.2f} seconds"
        else:
            message = f"Processing failed after {processing_time:.2f} seconds.\nCheck the logs for details."

        self.root.after(0, lambda: self.processing_finished(success, message))

    def ui_progress_callback(self, percentage: float, message: str):
        """Progress callback passed into processing functions to update UI."""
        try:
            pct = max(0, min(100, float(percentage)))
        except Exception:
            pct = 0
        # Schedule UI update on main thread
        self.root.after(0, lambda: self._apply_progress_update(pct, message))

    def _apply_progress_update(self, pct: float, message: str):
        try:
            self.progress_bar['value'] = pct
            self.progress_text_label.config(text=message)
        except Exception:
            pass

    def generate_report(self, results_data, args, processing_time: float):
        """Generate a detailed processing report."""
        logger = get_logger()
        config = get_config()

        # Generate report file path
        base_filename = os.path.splitext(os.path.basename(args.video_path))[0]
        input_dir = os.path.dirname(args.video_path)
        report_file = os.path.join(input_dir, f"{base_filename}{config.report_suffix}")

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 50 + "\n")
                f.write("VIDEO CLEANER - PROCESSING REPORT\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"Input Video: {args.video_path}\n")
                f.write(f"Output Video: {args.output_path}\n")
                f.write(f"Subtitle File: {args.srt_path or 'N/A'}\n")
                f.write(f"Foul Words File: {args.words_file}\n")
                backend = get_config().transcription_backend
                if backend == 'whisper':
                    f.write(f"Transcription: Whisper ({args.model})\n")
                else:
                    f.write(f"Transcription: Voxtral ({get_config().voxtral_model})\n")
                f.write(f"Processing Time: {processing_time:.2f} seconds\n\n")

                # Statistics
                stats_obj = getattr(results_data, "stats", None)
                f.write("--- Processing Statistics ---\n")
                if stats_obj is None:
                    stats_dict = {}
                else:
                    stats_dict = stats_obj.to_dict()

                if args.srt_path:
                    f.write("Processing Mode: Subtitle Verification\n")
                    f.write(f"Whisper - Potential Foul Words Found: {stats_dict.get('whisper_potential', 0)}\n")
                    f.write(f"Verification - Instances Verified for Mute: {stats_dict.get('verified_for_mute', 0)}\n")
                    f.write(f"Verification - Skipped (Text Mismatch): {stats_dict.get('skipped_text_mismatch', 0)}\n")
                    f.write(f"Verification - Skipped (No Overlapping Subtitle): {stats_dict.get('skipped_no_subtitle', 0)}\n")
                else:
                    f.write("Processing Mode: AI-Only Detection\n")
                    f.write(f"Whisper - Potential Foul Words Found: {stats_dict.get('whisper_potential', 0)}\n")
                    f.write(f"AI-Only - Instances Muted: {stats_dict.get('ai_only_muted', 0)}\n")

                mute_segments_list = getattr(results_data, "mute_segments", [])
                f.write(f"Final Mute Segments: {len(mute_segments_list)}\n")
                f.write("-" * 30 + "\n\n")

                # Mute segments
                mute_segments = mute_segments_list
                f.write("--- Mute Segments Applied ---\n")
                if mute_segments:
                    total_muted_time = sum(end - start for start, end in mute_segments)
                    f.write(f"Total Muted Duration: {total_muted_time:.3f} seconds\n")
                    for i, (start, end) in enumerate(mute_segments, 1):
                        f.write(f"{i}. {start:.3f}s - {end:.3f}s (duration: {end-start:.3f}s)\n")
                else:
                    f.write("No segments were muted.\n")
                f.write("-" * 30 + "\n\n")

            logger.info(f"Processing report saved to: {report_file}")

        except Exception as e:
            logger.error(f"Failed to generate report: {e}")

    def processing_finished(self, success: bool, message: str):
        """Called when processing is finished to update the UI."""
        # Update UI state
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()

        if success:
            self.status_label.config(text="Completed", foreground='#008000')
            messagebox.showinfo("Success", message)
        else:
            self.status_label.config(text="Failed", foreground='#ff0000')
            messagebox.showerror("Error", message)

    def cleanup_gui_resources(self):
        """Clean up GUI resources and stop any running processes."""
        try:
            logger = get_logger()
            # Stop processing if running
            if self.processing:
                self.processing = False
                logger.info("Stopped processing due to cleanup")

            # Clear log queue
            try:
                while not self.log_queue.empty():
                    self.log_queue.get_nowait()
            except:
                pass

            # Clear log text widget to free memory
            try:
                self.log_text.config(state=tk.NORMAL)
                self.log_text.delete('1.0', tk.END)
                self.log_text.config(state=tk.DISABLED)
            except:
                pass

            logger.debug("GUI resources cleaned up")

        except Exception as e:
            # Don't raise exceptions during cleanup
            print(f"Warning: Error during GUI cleanup: {e}")

    def show_about(self):
        """Show an About dialog."""
        try:
            messagebox.showinfo(
                "About Video Cleaner",
                "Video Cleaner\nAI-Powered Foul Word Muter\n\n" 
                "Use the Main tab to select input files and settings.\n"
                "Progress updates and logs are available in real-time.\n\n"
                "© 2025"
            )
        except Exception:
            pass


def run_gui():
    """Run the GUI version of the application."""
    import time

    root = tk.Tk()
    app = VideoCleanerGUI(root)

    # Handle window closing with proper cleanup
    def on_closing():
        try:
            if app.processing:
                if messagebox.askokcancel("Quit", "Processing is in progress. Do you want to quit?"):
                    app.processing = False
                    app.cleanup_gui_resources()
                    time.sleep(0.5)  # Give processing thread time to stop
                    cleanup_now()  # Final cleanup
                    root.destroy()
            else:
                app.cleanup_gui_resources()
                cleanup_now()  # Final cleanup
                root.destroy()
        except Exception as e:
            # Ensure we can always close
            print(f"Error during shutdown: {e}")
            try:
                cleanup_now()
                root.destroy()
            except:
                import sys
                sys.exit(1)

    root.protocol("WM_DELETE_WINDOW", on_closing)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        # Handle Ctrl+C gracefully
        print("\nShutting down...")
        on_closing()
    except Exception as e:
        print(f"GUI error: {e}")
        cleanup_now()
        raise
