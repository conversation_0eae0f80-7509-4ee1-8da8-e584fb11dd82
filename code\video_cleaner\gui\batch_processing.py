"""
Batch processing dialog for processing multiple videos.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

from ..core.config import get_config
from ..utils.validation import validate_video_file, validate_subtitle_file, validate_words_file


@dataclass
class BatchItem:
    """Represents a single item in the batch processing queue."""
    video_path: str
    subtitle_path: str = ""
    output_path: str = ""
    status: str = "Pending"  # Pending, Processing, Completed, Failed
    error_message: str = ""
    processing_time: float = 0.0


class BatchProcessingDialog:
    """Dialog for batch processing multiple videos."""
    
    def __init__(self, parent, words_file: str = "", model_size: str = "medium"):
        self.parent = parent
        self.words_file = words_file
        self.model_size = model_size
        self.batch_items: List[BatchItem] = []
        self.processing = False
        self.processing_thread = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Batch Processing")
        self.dialog.geometry("900x600")
        self.dialog.minsize(800, 500)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """Setup the user interface."""
        # Header
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=15, pady=15)
        
        ttk.Label(
            header_frame, 
            text="📦 Batch Processing", 
            font=('Segoe UI', 16, 'bold')
        ).pack(side=tk.LEFT)
        
        # Configuration section
        config_frame = ttk.LabelFrame(self.dialog, text="⚙️ Configuration", padding=10)
        config_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        # Words file
        ttk.Label(config_frame, text="Foul Words File:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.words_file_var = tk.StringVar(value=self.words_file)
        words_entry = ttk.Entry(config_frame, textvariable=self.words_file_var, width=50)
        words_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 5), pady=5)
        ttk.Button(config_frame, text="Browse", command=self.browse_words_file).grid(row=0, column=2, padx=5, pady=5)
        
        # Model size
        ttk.Label(config_frame, text="AI Model:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_var = tk.StringVar(value=self.model_size)
        model_combo = ttk.Combobox(
            config_frame, 
            textvariable=self.model_var,
            values=["tiny", "base", "small", "medium", "large"],
            state="readonly",
            width=15
        )
        model_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 5), pady=5)
        
        config_frame.columnconfigure(1, weight=1)
        
        # File list section
        list_frame = ttk.LabelFrame(self.dialog, text="📁 Files to Process", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 10))
        
        # Buttons for file management
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="➕ Add Videos", command=self.add_videos).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📁 Add Folder", command=self.add_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ Remove Selected", command=self.remove_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        
        # File list with treeview
        tree_frame = ttk.Frame(list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview
        columns = ("Video File", "Subtitle File", "Output File", "Status")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=12)
        
        # Configure columns
        self.tree.heading("Video File", text="Video File")
        self.tree.heading("Subtitle File", text="Subtitle File (Optional)")
        self.tree.heading("Output File", text="Output File")
        self.tree.heading("Status", text="Status")
        
        self.tree.column("Video File", width=250)
        self.tree.column("Subtitle File", width=200)
        self.tree.column("Output File", width=250)
        self.tree.column("Status", width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky=tk.NSEW)
        v_scrollbar.grid(row=0, column=1, sticky=tk.NS)
        h_scrollbar.grid(row=1, column=0, sticky=tk.EW)
        
        tree_frame.rowconfigure(0, weight=1)
        tree_frame.columnconfigure(0, weight=1)
        
        # Progress section
        progress_frame = ttk.LabelFrame(self.dialog, text="📊 Progress", padding=10)
        progress_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="Ready to process")
        self.progress_label.pack(anchor=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Control buttons
        control_frame = ttk.Frame(self.dialog)
        control_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.start_button = ttk.Button(
            control_frame, 
            text="▶️ Start Processing", 
            command=self.start_processing
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(
            control_frame, 
            text="⏹️ Stop", 
            command=self.stop_processing,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="❌ Close", command=self.close_dialog).pack(side=tk.RIGHT)
        
        # Bind double-click to edit item
        self.tree.bind("<Double-1>", self.edit_item)
    
    def browse_words_file(self):
        """Browse for words file."""
        filename = filedialog.askopenfilename(
            title="Select Foul Words File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.words_file_var.set(filename)
    
    def add_videos(self):
        """Add video files to the batch."""
        filenames = filedialog.askopenfilenames(
            title="Select Video Files",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                ("All files", "*.*")
            ]
        )
        
        for filename in filenames:
            self.add_video_file(filename)
    
    def add_folder(self):
        """Add all video files from a folder."""
        folder = filedialog.askdirectory(title="Select Folder with Videos")
        if not folder:
            return
        
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
        video_files = []
        
        for root, dirs, files in os.walk(folder):
            for file in files:
                if Path(file).suffix.lower() in video_extensions:
                    video_files.append(os.path.join(root, file))
        
        if not video_files:
            messagebox.showinfo("No Videos Found", f"No video files found in {folder}")
            return
        
        for video_file in video_files:
            self.add_video_file(video_file)
        
        messagebox.showinfo("Videos Added", f"Added {len(video_files)} video files from {folder}")
    
    def add_video_file(self, video_path: str):
        """Add a single video file to the batch."""
        # Check if already exists
        for item in self.batch_items:
            if item.video_path == video_path:
                return
        
        # Generate default output path
        config = get_config()
        base_name = os.path.splitext(video_path)[0]
        extension = os.path.splitext(video_path)[1]
        output_path = f"{base_name}{config.output_suffix}{extension}"
        
        # Look for subtitle file in same directory
        subtitle_path = ""
        srt_path = f"{base_name}.srt"
        if os.path.exists(srt_path):
            subtitle_path = srt_path
        
        # Create batch item
        item = BatchItem(
            video_path=video_path,
            subtitle_path=subtitle_path,
            output_path=output_path
        )
        
        self.batch_items.append(item)
        self.update_tree()
    
    def remove_selected(self):
        """Remove selected items from the batch."""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("No Selection", "Please select items to remove.")
            return
        
        # Get indices of selected items
        indices_to_remove = []
        for item_id in selected_items:
            index = self.tree.index(item_id)
            indices_to_remove.append(index)
        
        # Remove items in reverse order to maintain indices
        for index in sorted(indices_to_remove, reverse=True):
            del self.batch_items[index]
        
        self.update_tree()
    
    def clear_all(self):
        """Clear all items from the batch."""
        if self.batch_items and messagebox.askyesno("Confirm Clear", "Remove all items from the batch?"):
            self.batch_items.clear()
            self.update_tree()
    
    def edit_item(self, event):
        """Edit the selected item."""
        selected = self.tree.selection()
        if not selected:
            return
        
        index = self.tree.index(selected[0])
        item = self.batch_items[index]
        
        # Create edit dialog
        EditItemDialog(self.dialog, item, self.update_tree)
    
    def update_tree(self):
        """Update the treeview with current batch items."""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add current items
        for item in self.batch_items:
            video_name = os.path.basename(item.video_path)
            subtitle_name = os.path.basename(item.subtitle_path) if item.subtitle_path else ""
            output_name = os.path.basename(item.output_path)
            
            self.tree.insert("", tk.END, values=(video_name, subtitle_name, output_name, item.status))
    
    def start_processing(self):
        """Start batch processing."""
        if not self.batch_items:
            messagebox.showwarning("No Files", "Please add video files to process.")
            return
        
        if not self.words_file_var.get():
            messagebox.showerror("No Words File", "Please select a foul words file.")
            return
        
        # Validate words file
        words_validation = validate_words_file(self.words_file_var.get())
        if not words_validation.is_valid:
            messagebox.showerror("Invalid Words File", words_validation.error_message)
            return
        
        # Update UI state
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.config(maximum=len(self.batch_items), value=0)
        
        # Start processing thread
        self.processing_thread = threading.Thread(target=self.process_batch, daemon=True)
        self.processing_thread.start()
    
    def stop_processing(self):
        """Stop batch processing."""
        self.processing = False
        self.progress_label.config(text="Stopping...")
    
    def process_batch(self):
        """Process all items in the batch."""
        from ..main import process_video_cli
        
        completed = 0
        failed = 0
        
        for i, item in enumerate(self.batch_items):
            if not self.processing:
                break
            
            # Update progress
            self.dialog.after(0, lambda: self.progress_label.config(
                text=f"Processing {os.path.basename(item.video_path)}..."
            ))
            self.dialog.after(0, lambda: self.progress_bar.config(value=i))
            
            # Update item status
            item.status = "Processing"
            self.dialog.after(0, self.update_tree)
            
            try:
                # Create args object for processing
                class Args:
                    def __init__(self, item: BatchItem, words_file: str, model: str):
                        self.video_path = item.video_path
                        self.srt_path = item.subtitle_path if item.subtitle_path else None
                        self.words_file = words_file
                        self.model = model
                        self.output_path = item.output_path
                
                args = Args(item, self.words_file_var.get(), self.model_var.get())
                
                # Process the video
                import time
                start_time = time.time()
                success = process_video_cli(args)
                processing_time = time.time() - start_time
                
                if success:
                    item.status = "Completed"
                    item.processing_time = processing_time
                    completed += 1
                else:
                    item.status = "Failed"
                    item.error_message = "Processing failed"
                    failed += 1
                    
            except Exception as e:
                item.status = "Failed"
                item.error_message = str(e)
                failed += 1
            
            # Update tree
            self.dialog.after(0, self.update_tree)
        
        # Update final progress
        self.dialog.after(0, lambda: self.progress_bar.config(value=len(self.batch_items)))
        self.dialog.after(0, lambda: self.progress_label.config(
            text=f"Completed: {completed}, Failed: {failed}"
        ))
        
        # Update UI state
        self.dialog.after(0, lambda: self.start_button.config(state=tk.NORMAL))
        self.dialog.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
        self.processing = False
        
        # Show completion message
        if completed > 0:
            self.dialog.after(0, lambda: messagebox.showinfo(
                "Batch Complete", 
                f"Batch processing completed!\n\nCompleted: {completed}\nFailed: {failed}"
            ))
    
    def close_dialog(self):
        """Close the dialog."""
        if self.processing:
            if messagebox.askyesno("Processing Active", "Processing is active. Stop and close?"):
                self.processing = False
                self.dialog.destroy()
        else:
            self.dialog.destroy()


class EditItemDialog:
    """Dialog for editing a batch item."""
    
    def __init__(self, parent, item: BatchItem, update_callback):
        self.item = item
        self.update_callback = update_callback
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Edit Batch Item")
        self.dialog.geometry("600x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.setup_ui()
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """Setup the edit dialog UI."""
        # Video file (read-only)
        ttk.Label(self.dialog, text="Video File:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        ttk.Label(self.dialog, text=self.item.video_path, foreground="gray").grid(
            row=0, column=1, columnspan=2, sticky=tk.W+tk.E, padx=10, pady=10)
        
        # Subtitle file
        ttk.Label(self.dialog, text="Subtitle File:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        self.subtitle_var = tk.StringVar(value=self.item.subtitle_path)
        ttk.Entry(self.dialog, textvariable=self.subtitle_var).grid(
            row=1, column=1, sticky=tk.W+tk.E, padx=10, pady=5)
        ttk.Button(self.dialog, text="Browse", command=self.browse_subtitle).grid(
            row=1, column=2, padx=10, pady=5)
        
        # Output file
        ttk.Label(self.dialog, text="Output File:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        self.output_var = tk.StringVar(value=self.item.output_path)
        ttk.Entry(self.dialog, textvariable=self.output_var).grid(
            row=2, column=1, sticky=tk.W+tk.E, padx=10, pady=5)
        ttk.Button(self.dialog, text="Browse", command=self.browse_output).grid(
            row=2, column=2, padx=10, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="Save", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.LEFT, padx=5)
        
        # Configure grid weights
        self.dialog.columnconfigure(1, weight=1)
    
    def browse_subtitle(self):
        """Browse for subtitle file."""
        filename = filedialog.askopenfilename(
            title="Select Subtitle File",
            filetypes=[("SRT files", "*.srt"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.subtitle_var.set(filename)
    
    def browse_output(self):
        """Browse for output file."""
        filename = filedialog.asksaveasfilename(
            title="Save Output As",
            filetypes=[("Video files", "*.mp4 *.avi *.mov"), ("All files", "*.*")],
            defaultextension=".mp4"
        )
        if filename:
            self.output_var.set(filename)
    
    def save_changes(self):
        """Save changes to the batch item."""
        self.item.subtitle_path = self.subtitle_var.get()
        self.item.output_path = self.output_var.get()
        
        if self.update_callback:
            self.update_callback()
        
        self.dialog.destroy()
