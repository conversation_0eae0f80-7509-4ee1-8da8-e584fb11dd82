"""
Subtitle parsing functionality for SRT files.
"""

import logging
from typing import List, Optional
import srt
import mimetypes
from pathlib import Path

logger = logging.getLogger(__name__)


def parse_subtitles(srt_path: str) -> Optional[List[srt.Subtitle]]:
    """
    Parses an SRT subtitle file.
    
    Args:
        srt_path: Path to the SRT subtitle file
        
    Returns:
        List of subtitle objects or None if parsing failed
    """
    logger.info(f"Parsing subtitle file '{srt_path}'...")
    
    try:
        # Validate file exists and is readable
        path = Path(srt_path)
        if not path.exists():
            logger.error(f"Subtitle file not found at '{srt_path}'")
            return None
        
        if not path.is_file():
            logger.error(f"Path is not a file: '{srt_path}'")
            return None
        
        # Check file size (reasonable limit)
        file_size = path.stat().st_size
        max_size = 50 * 1024 * 1024  # 50MB limit for subtitle files
        if file_size > max_size:
            logger.error(f"Subtitle file too large: {file_size} bytes (max: {max_size})")
            return None
        
        # Try to detect encoding and read file
        encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']
        subtitle_content = None
        
        for encoding in encodings:
            try:
                with open(srt_path, 'r', encoding=encoding) as f:
                    subtitle_content = f.read()
                logger.debug(f"Successfully read file with encoding: {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if subtitle_content is None:
            logger.error(f"Could not read subtitle file with any supported encoding: {encodings}")
            return None
        
        # Basic check for empty file before parsing
        if not subtitle_content.strip():
            logger.warning(f"Subtitle file '{srt_path}' is empty.")
            return []  # Return empty list, not None, to distinguish from read errors

        # Parse the SRT content
        try:
            subtitle_generator = srt.parse(subtitle_content)
            subtitles = list(subtitle_generator)  # Consume generator
        except Exception as parse_error:
            logger.error(f"Error parsing SRT content: {parse_error}")
            return None

        if not subtitles:
            # This might happen if the format is invalid despite content being present
            logger.warning(f"Could not parse any valid subtitle entries from '{srt_path}'. "
                         f"File might be malformed or not in SRT format.")
            return []
        
        # Validate subtitle entries
        valid_subtitles = []
        for i, subtitle in enumerate(subtitles):
            try:
                # Check that subtitle has required attributes
                if not hasattr(subtitle, 'start') or not hasattr(subtitle, 'end') or not hasattr(subtitle, 'content'):
                    logger.warning(f"Subtitle entry {i+1} missing required attributes, skipping")
                    continue
                
                # Check timing validity
                start_seconds = subtitle.start.total_seconds()
                end_seconds = subtitle.end.total_seconds()
                
                if start_seconds < 0 or end_seconds < 0:
                    logger.warning(f"Subtitle entry {i+1} has negative timestamps, skipping")
                    continue
                
                if start_seconds >= end_seconds:
                    logger.warning(f"Subtitle entry {i+1} has invalid timing (start >= end), skipping")
                    continue
                
                # Check content validity
                if not subtitle.content or not subtitle.content.strip():
                    logger.warning(f"Subtitle entry {i+1} has empty content, skipping")
                    continue
                
                valid_subtitles.append(subtitle)
                
            except Exception as e:
                logger.warning(f"Error validating subtitle entry {i+1}: {e}, skipping")
                continue
        
        if len(valid_subtitles) != len(subtitles):
            logger.info(f"Filtered {len(subtitles)} subtitle entries down to {len(valid_subtitles)} valid entries")
        
        logger.info(f"Parsed {len(valid_subtitles)} valid subtitle entries.")
        return valid_subtitles
        
    except FileNotFoundError:
        logger.error(f"Subtitle file not found at '{srt_path}'")
        return None
    except PermissionError:
        logger.error(f"Permission denied reading subtitle file: '{srt_path}'")
        return None
    except Exception as e:
        logger.exception(f"Error parsing subtitle file '{srt_path}': {e}")
        return None


def validate_subtitle_file(srt_path: str) -> tuple[bool, str]:
    """
    Validate a subtitle file without fully parsing it.
    
    Args:
        srt_path: Path to the subtitle file
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        path = Path(srt_path)
        
        if not path.exists():
            return False, f"File does not exist: {srt_path}"
        
        if not path.is_file():
            return False, f"Path is not a file: {srt_path}"
        
        # Check file extension
        if path.suffix.lower() not in ['.srt', '.txt']:
            return False, f"File does not have .srt or .txt extension: {path.suffix}"
        
        # Check file size
        file_size = path.stat().st_size
        if file_size == 0:
            return False, "File is empty"
        
        max_size = 50 * 1024 * 1024  # 50MB
        if file_size > max_size:
            return False, f"File too large ({file_size} bytes > {max_size})"
        
        # Try to read first few lines to check format
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                first_lines = [f.readline().strip() for _ in range(5)]
            
            # Basic SRT format check - first line should be a number
            if first_lines and first_lines[0] and not first_lines[0].isdigit():
                return False, "File does not appear to be in SRT format (first line should be a number)"
                
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(srt_path, 'r', encoding='latin1') as f:
                    first_lines = [f.readline().strip() for _ in range(5)]
            except Exception:
                return False, "Could not read file with supported encodings"
        
        return True, "Valid subtitle file"
        
    except Exception as e:
        return False, f"Error validating file: {e}"


def get_subtitle_stats(subtitles: List[srt.Subtitle]) -> dict:
    """
    Get statistics about a list of subtitles.
    
    Args:
        subtitles: List of subtitle objects
        
    Returns:
        Dictionary with subtitle statistics
    """
    if not subtitles:
        return {
            "count": 0,
            "total_duration": 0,
            "average_duration": 0,
            "total_characters": 0,
            "average_characters": 0
        }
    
    total_duration = 0
    total_characters = 0
    
    for subtitle in subtitles:
        duration = subtitle.end.total_seconds() - subtitle.start.total_seconds()
        total_duration += duration
        total_characters += len(subtitle.content)
    
    return {
        "count": len(subtitles),
        "total_duration": total_duration,
        "average_duration": total_duration / len(subtitles),
        "total_characters": total_characters,
        "average_characters": total_characters / len(subtitles)
    }
